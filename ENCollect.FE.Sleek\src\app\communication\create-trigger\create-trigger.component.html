<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <h2 class="title">Create Communication Trigger</h2>
  <div class="enc-card" [formGroup]="createForm">
    <div class="card-header border-bottom-0">
      <div>
        <h3>Create Communication Trigger</h3>
        <h5 class="mt-3">
          Set up automated communications based on specific financial events
        </h5>
      </div>
    </div>
    <div class="card-content">
      <div class="row">
        <div class="col-md-6">
          <div class="form-control-group">
            <label class="form-label required">Trigger Name</label>
            <input
              type="text"
              class="form-control"
              name="trigger-name"
              id="triggerName"
              placeholder="e.g., 7 Day Payment Reminder"
              formControlName="triggerName"
            />
          </div>
        </div>
        <!-- <div class="col-md-6">
          <div class="form-control-group">
            <label class="form-label required">Status</label>
            <div class="form-toggle-group">
              <label>
                <input
                  type="checkbox"
                  name="trigger-status"
                  id="triggerStatus"
                  formControlName="status"
                />
                <span>
                  {{ fValue?.status ? "Active" : "Inactive" }}
                </span>
              </label>
            </div>
          </div>
        </div> -->
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label">Description</label>
            <textarea
              class="form-control"
              name="trigger-description"
              id="triggerDescription"
              rows="4"
              placeholder="Brief description of this trigger's purpose"
              formControlName="description"
            ></textarea>
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Trigger Type</label>
            <div
              class="trigger-type-btn-group"
              btnRadioGroup
              formControlName="triggerType"
            >
              @for (item of triggerTypes; track item.id) {
              <button
                class="trigger-type-btn"
                [btnRadio]="item?.id"
                
              >
            <!--   [disabled]="!item?.enabled"
                id="trigger-type-{{ item?.id }}" -->
                <div class="trigger-name">
                  <svg-icon src="assets/new/svgs/calendar-icon.svg"></svg-icon>
                  <span>Trigger type {{ item?.id }}: {{ item?.name }}</span>
                </div>
                <div class="trigger-desc">{{ item?.desc }}</div>
                <svg-icon
                  class="active-check-icon"
                  src="assets/new/svgs/check_circle_fill.svg"
                ></svg-icon>
              </button>
              }
            </div>
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <div class="alert alert-primary">
              {{ activeTriggerType?.detail }}
            </div>
          </div>
        </div>

        @if (activeTriggerType?.xLabel) {
        <div class="col-md-3">
          <div class="form-control-group">
            <label class="form-label"> {{ activeTriggerType?.xLabel }} </label>
            <input
              type="number"
              class="form-control"
              name="x-value"
              id="xValue"
              formControlName="xValue"
            />
          </div>
        </div>
        }

        <div class="w-full"></div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Communication Channel</label>
            <div
              class="form-button-group d-flex"
              btnRadioGroup
              formControlName="activeChannel"
            >
              <button
                class="flex-fill"
                [btnRadio]="'email'"
                id="com-channel-email"
              >
                Email
              </button>
              <button class="flex-fill" [btnRadio]="'sms'" id="com-channel-sms">
                SMS
              </button>
              <button
                class="flex-fill"
                [btnRadio]="'letter'"
                id="com-channel-letter"
              >
                Letter
              </button>
            </div>
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label"> Template </label>
            @if (fValue?.activeChannel === 'email') {
            <ng-select
              class="form-ng-select"
              [items]="emailTemplates"
              bindLabel="name"
              placeholder="Select Email Template"
              name="template"
              id="template"
              [clearable]="false"
              [searchable]="true"
              [appendTo]="'body'"
              formControlName="emailTemplate"
            >
            </ng-select>
            } @else if (fValue?.activeChannel === 'sms') {
            <ng-select
              class="form-ng-select"
              [items]="smsTemplates"
              bindLabel="name"
              placeholder="Select SMS Template"
              name="template"
              id="template"
              [clearable]="false"
              [searchable]="true"
              [appendTo]="'body'"
              formControlName="smsTemplate"
            >
            </ng-select>
            } @else if (fValue?.activeChannel === 'letter') {
            <ng-select
              class="form-ng-select"
              [items]="letterTemplates"
              bindLabel="name"
              placeholder="Select Letter Template"
              name="template"
              id="template"
              [clearable]="false"
              [searchable]="true"
              [appendTo]="'body'"
              formControlName="letterTemplate"
            >
            </ng-select>
            }
          </div>
        </div>

        <div class="col-md-3">
          <div class="form-control-group">
            <label class="form-label"> Maximum Occurrences </label>
            <select
              class="form-select"
              name="max-occurences"
              id="maxOccurences"
              formControlName="maxOccurrences"
            >
              <option value="1">1 time only</option>
              <option value="2">2 times</option>
              <option value="3">3 times</option>
              <option value="5">5 times</option>
              <option value="10">10 times</option>
              <option value="unlimited">Unlimited</option>
            </select>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <button class="btn btn-secondary mw-150px me-4">Create Trigger</button>
      <button class="btn btn-outline-primary mw-150px">Cancel</button>
    </div>
  </div>
</div>
