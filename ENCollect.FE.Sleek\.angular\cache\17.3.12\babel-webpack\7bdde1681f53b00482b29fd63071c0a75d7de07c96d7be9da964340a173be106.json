{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormsModule, Validators } from \"@angular/forms\";\nimport { SharedModule } from \"src/app/shared\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.currentVariable = '';\n    this.currentStartPos = 0;\n    this.currentEndPos = 0;\n    this.currentLanguageIndex = 0;\n    this.selectedLanguagesForAdd = [];\n    // Sample database field suggestions\n    this.suggestedFields = ['user.first_name', 'user.last_name', 'user.email', 'user.phone', 'account.number', 'account.balance', 'payment.amount', 'payment.due_date', 'company.name'];\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.allLanguages = [{\n      name: \"English\",\n      code: \"en\"\n    }, {\n      name: \"Hindi\",\n      code: \"hi\"\n    }, {\n      name: \"Tamil\",\n      code: \"ta\"\n    }, {\n      name: \"Bengali\",\n      code: \"bn\"\n    }, {\n      name: \"Telugu\",\n      code: \"te\"\n    }, {\n      name: \"Marathi\",\n      code: \"mr\"\n    }, {\n      name: \"Urdu\",\n      code: \"ur\"\n    }, {\n      name: \"Gujarati\",\n      code: \"gu\"\n    }, {\n      name: \"Kannada\",\n      code: \"kn\"\n    }, {\n      name: \"Malayalam\",\n      code: \"ml\"\n    }, {\n      name: \"Odia\",\n      code: \"or\"\n    }, {\n      name: \"Punjabi\",\n      code: \"pa\"\n    }, {\n      name: \"Assamese\",\n      code: \"as\"\n    }, {\n      name: \"Maithili\",\n      code: \"mai\"\n    }, {\n      name: \"Santali\",\n      code: \"sat\"\n    }, {\n      name: \"Kashmiri\",\n      code: \"ks\"\n    }, {\n      name: \"Konkani\",\n      code: \"kok\"\n    }, {\n      name: \"Sindhi\",\n      code: \"sd\"\n    }, {\n      name: \"Sanskrit\",\n      code: \"sa\"\n    }, {\n      name: \"Manipuri\",\n      code: \"mni\"\n    }, {\n      name: \"Bodo\",\n      code: \"brx\"\n    }, {\n      name: \"Dogri\",\n      code: \"doi\"\n    }];\n    this.buildCreateTemplateForm();\n    this.buildVariableMappingForm();\n    this.buildAddLanguageForm();\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\"],\n      templateName: [null, [Validators.required]],\n      activeLanguage: [this.allLanguages[0].code],\n      languages: this.buildLanguagesFormArray()\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    data = data || [this.allLanguages[0]];\n    data?.forEach(o => {\n      formArray.push(this.buildLanguageFormGroup(o));\n    });\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]]\n    });\n  }\n  buildVariableMappingForm() {\n    this.variableMappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n  buildAddLanguageForm() {\n    this.addLanguageForm = this.fb.group({\n      searchTerm: ['']\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  ngAfterViewInit() {\n    console.log('AfterViewInit - Modal template reference:', this.variableMappingModal);\n  }\n  onTemplateBodyClick(event, languageIndex) {\n    console.log('Template body clicked!');\n    console.log('Channel type:', this.fValue?.channelType);\n    // Only handle clicks for SMS channel type\n    if (this.fValue?.channelType !== 'sms') {\n      console.log('Not SMS channel, ignoring click');\n      return;\n    }\n    const target = event.target;\n    const text = target.value || '';\n    const cursorPosition = target.selectionStart || 0;\n    console.log('Text:', text);\n    console.log('Cursor position:', cursorPosition);\n    // Find if cursor is within a [Var] placeholder\n    const varPattern = /\\[Var\\]/gi;\n    let match;\n    while ((match = varPattern.exec(text)) !== null) {\n      const start = match.index;\n      const end = match.index + match[0].length;\n      console.log(`Found [Var] at position ${start}-${end}, cursor at ${cursorPosition}`);\n      if (cursorPosition >= start && cursorPosition <= end) {\n        console.log('Cursor is within [Var], opening modal');\n        // Try modal first, fallback to simple prompt\n        try {\n          this.openVariableMappingModal(match[0], start, end, languageIndex);\n        } catch (error) {\n          console.error('Modal failed, using simple prompt:', error);\n          this.openSimpleVariableMapping(match[0], start, end, languageIndex);\n        }\n        break;\n      }\n    }\n  }\n  openVariableMappingModal(variable, startPos, endPos, languageIndex) {\n    console.log('openVariableMappingModal called with:', variable);\n    console.log('Modal template reference:', this.variableMappingModal);\n    console.log('Modal service:', this.modalService);\n    // First, let's test with a simple alert\n    alert(`Variable mapping requested for: ${variable} at position ${startPos}-${endPos}`);\n    // Store current mapping context\n    this.currentVariable = variable;\n    this.currentStartPos = startPos;\n    this.currentEndPos = endPos;\n    this.currentLanguageIndex = languageIndex;\n    // Reset form\n    this.variableMappingForm.reset();\n    // Check if template reference exists\n    if (!this.variableMappingModal) {\n      console.error('Modal template reference not found!');\n      alert('Modal template not found. Please check the template reference.');\n      return;\n    }\n    try {\n      // Open modal\n      console.log('Attempting to open modal...');\n      this.modalRef = this.modalService.show(this.variableMappingModal, {\n        class: 'modal-dialog-centered',\n        ignoreBackdropClick: true\n      });\n      console.log('Modal opened successfully:', this.modalRef);\n    } catch (error) {\n      console.error('Error opening modal:', error);\n      alert('Error opening modal: ' + error);\n    }\n  }\n  updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const templateBodyControl = languageGroup.get('templateBody');\n    if (templateBodyControl) {\n      const currentText = templateBodyControl.value || '';\n      const newText = currentText.substring(0, startPos) + `[${mappedField}]` + currentText.substring(endPos);\n      templateBodyControl.setValue(newText);\n    }\n  }\n  getVariableCount(templateBody) {\n    if (!templateBody) return 0;\n    const varPattern = /\\[Var\\]/gi;\n    const matches = templateBody.match(varPattern);\n    return matches ? matches.length : 0;\n  }\n  getMappedVariableCount(templateBody) {\n    if (!templateBody) return 0;\n    // Count variables that are not [Var] (i.e., already mapped)\n    const allVarPattern = /\\[[^\\]]+\\]/g;\n    const unmappedVarPattern = /\\[Var\\]/gi;\n    const allMatches = templateBody.match(allVarPattern);\n    const unmappedMatches = templateBody.match(unmappedVarPattern);\n    const totalVars = allMatches ? allMatches.length : 0;\n    const unmappedVars = unmappedMatches ? unmappedMatches.length : 0;\n    return totalVars - unmappedVars;\n  }\n  onHeaderUpload(event) {\n    // Handle header upload for letter type\n  }\n  onFooterUpload(event) {\n    // Handle footer upload for letter type\n  }\n  // Template-based modal methods\n  useSuggestedField(field) {\n    this.variableMappingForm.patchValue({\n      databaseField: field\n    });\n  }\n  mapVariableFromTemplate() {\n    if (this.variableMappingForm.valid) {\n      const mappedField = this.variableMappingForm.get('databaseField')?.value;\n      this.updateTemplateBodyWithMapping(mappedField, this.currentStartPos, this.currentEndPos, this.currentLanguageIndex);\n      this.closeVariableModal();\n    }\n  }\n  closeVariableModal() {\n    this.modalRef?.hide();\n    this.variableMappingForm.reset();\n  }\n  // Alternative method using a simple prompt for testing\n  openSimpleVariableMapping(variable, startPos, endPos, languageIndex) {\n    const mappedField = prompt(`Map variable \"${variable}\" to database field:`, 'user.first_name');\n    if (mappedField) {\n      this.updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex);\n    }\n  }\n  // Add Language Modal Methods\n  openAddLanguageModal() {\n    // Reset form and selection\n    this.addLanguageForm.reset();\n    this.selectedLanguagesForAdd = [];\n    // Check if template reference exists\n    if (!this.addLanguageModal) {\n      alert('Add Language modal template not found.');\n      return;\n    }\n    // Open modal\n    this.modalRef = this.modalService.show(this.addLanguageModal, {\n      class: 'modal-dialog-centered modal-lg',\n      ignoreBackdropClick: true\n    });\n  }\n  closeAddLanguageModal() {\n    this.modalRef?.hide();\n    this.addLanguageForm.reset();\n    this.selectedLanguagesForAdd = [];\n  }\n  getFilteredAvailableLanguages() {\n    const searchTerm = this.addLanguageForm.get('searchTerm')?.value?.toLowerCase() || '';\n    const currentLanguageCodes = this.fValue?.languages?.map(lang => lang.languageCode) || [];\n    return this.allLanguages.filter(lang => !currentLanguageCodes.includes(lang.code) && (lang.name.toLowerCase().includes(searchTerm) || lang.code.toLowerCase().includes(searchTerm)));\n  }\n  isLanguageSelected(language) {\n    return this.selectedLanguagesForAdd.some(lang => lang.code === language.code);\n  }\n  toggleLanguageSelection(language) {\n    const index = this.selectedLanguagesForAdd.findIndex(lang => lang.code === language.code);\n    if (index > -1) {\n      this.selectedLanguagesForAdd.splice(index, 1);\n    } else {\n      this.selectedLanguagesForAdd.push(language);\n    }\n  }\n  removeLanguageFromSelection(language) {\n    const index = this.selectedLanguagesForAdd.findIndex(lang => lang.code === language.code);\n    if (index > -1) {\n      this.selectedLanguagesForAdd.splice(index, 1);\n    }\n  }\n  addSelectedLanguages() {\n    if (this.selectedLanguagesForAdd.length === 0) return;\n    const languagesArray = this.createForm.get('languages');\n    // Add each selected language to the form array\n    this.selectedLanguagesForAdd.forEach(language => {\n      const newLanguageGroup = this.buildLanguageFormGroup(language);\n      languagesArray.push(newLanguageGroup);\n    });\n    // Close modal\n    this.closeAddLanguageModal();\n  }\n  removeLanguage(index) {\n    const languagesArray = this.createForm.get('languages');\n    if (languagesArray.length <= 1) {\n      alert('At least one language is required.');\n      return;\n    }\n    const languageToRemove = this.fValue?.languages?.[index];\n    const confirmMessage = `Are you sure you want to remove \"${languageToRemove?.languageName}\" language? This will delete all content for this language.`;\n    if (confirm(confirmMessage)) {\n      languagesArray.removeAt(index);\n      // If the removed language was the active one, switch to the first available language\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n      if (currentActiveLanguage === languageToRemove?.languageCode) {\n        const firstLanguage = this.fValue?.languages?.[0];\n        if (firstLanguage) {\n          this.createForm.patchValue({\n            activeLanguage: firstLanguage.languageCode\n          });\n        }\n      }\n    }\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      variableMappingModal: [{\n        type: ViewChild,\n        args: ['variableMappingModal']\n      }],\n      addLanguageModal: [{\n        type: ViewChild,\n        args: ['addLanguageModal']\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, SharedModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormArray", "FormBuilder", "FormsModule", "Validators", "SharedModule", "BsModalService", "CreateTemplateComponent", "constructor", "fb", "modalService", "currentVariable", "currentStartPos", "currentEndPos", "currentLanguageIndex", "selectedLanguagesForAdd", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbData", "label", "allLanguages", "name", "code", "buildCreateTemplateForm", "buildVariableMappingForm", "buildAddLanguageForm", "createForm", "group", "channelType", "templateName", "required", "activeLanguage", "languages", "buildLanguagesFormArray", "data", "formArray", "for<PERSON>ach", "o", "push", "buildLanguageFormGroup", "languageCode", "languageName", "emailSubject", "templateBody", "variableMappingForm", "databaseField", "addLanguageForm", "searchTerm", "fValue", "value", "ngAfterViewInit", "console", "log", "variableMappingModal", "onTemplateBodyClick", "event", "languageIndex", "target", "text", "cursorPosition", "selectionStart", "varPattern", "match", "exec", "start", "index", "end", "length", "openVariableMappingModal", "error", "openSimpleVariableMapping", "variable", "startPos", "endPos", "alert", "reset", "modalRef", "show", "class", "ignoreBackdropClick", "updateTemplateBodyWithMapping", "mappedField", "languagesArray", "get", "languageGroup", "at", "templateBodyControl", "currentText", "newText", "substring", "setValue", "getVariableCount", "matches", "getMappedVariableCount", "allVarPattern", "unmappedVarPattern", "allMatches", "unmappedMatches", "totalVars", "unmappedVars", "onHeaderUpload", "onFooterUpload", "useSuggestedField", "field", "patchValue", "mapVariableFromTemplate", "valid", "closeVariableModal", "hide", "prompt", "openAddLanguageModal", "addLanguageModal", "closeAddLanguageModal", "getFilteredAvailableLanguages", "toLowerCase", "currentLanguageCodes", "map", "lang", "filter", "includes", "isLanguageSelected", "language", "some", "toggleLanguageSelection", "findIndex", "splice", "removeLanguageFromSelection", "addSelectedLanguages", "newLanguageGroup", "removeLanguage", "languageToRemove", "confirmMessage", "confirm", "removeAt", "currentActiveLanguage", "firstLanguage", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, ViewChild, TemplateRef, AfterViewInit } from \"@angular/core\";\r\nimport {\r\n  Form<PERSON>rray,\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from \"@angular/forms\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { BsModalService, BsModalRef } from \"ngx-bootstrap/modal\";\r\n\r\n@Component({\r\n  selector: \"app-create-template\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule],\r\n  templateUrl: \"./create-template.component.html\",\r\n  styleUrl: \"./create-template.component.scss\",\r\n})\r\nexport class CreateTemplateComponent implements AfterViewInit {\r\n  private fb: FormBuilder = inject(FormBuilder);\r\n  private modalService: BsModalService = inject(BsModalService);\r\n  modalRef?: BsModalRef;\r\n\r\n  @ViewChild('variableMappingModal') variableMappingModal!: TemplateRef<any>;\r\n  @ViewChild('addLanguageModal') addLanguageModal!: TemplateRef<any>;\r\n\r\n  // Variable mapping form and data\r\n  variableMappingForm!: FormGroup;\r\n  currentVariable: string = '';\r\n  currentStartPos: number = 0;\r\n  currentEndPos: number = 0;\r\n  currentLanguageIndex: number = 0;\r\n\r\n  // Add language form and data\r\n  addLanguageForm!: FormGroup;\r\n  selectedLanguagesForAdd: any[] = [];\r\n\r\n  // Sample database field suggestions\r\n  suggestedFields = [\r\n    'user.first_name',\r\n    'user.last_name',\r\n    'user.email',\r\n    'user.phone',\r\n    'account.number',\r\n    'account.balance',\r\n    'payment.amount',\r\n    'payment.due_date',\r\n    'company.name'\r\n  ];\r\n\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Create Communication Template\" },\r\n  ];\r\n  allLanguages: any[] = [\r\n    { name: \"English\", code: \"en\" },\r\n    { name: \"Hindi\", code: \"hi\" },\r\n    { name: \"Tamil\", code: \"ta\" },\r\n    { name: \"Bengali\", code: \"bn\" },\r\n    { name: \"Telugu\", code: \"te\" },\r\n    { name: \"Marathi\", code: \"mr\" },\r\n    { name: \"Urdu\", code: \"ur\" },\r\n    { name: \"Gujarati\", code: \"gu\" },\r\n    { name: \"Kannada\", code: \"kn\" },\r\n    { name: \"Malayalam\", code: \"ml\" },\r\n    { name: \"Odia\", code: \"or\" },\r\n    { name: \"Punjabi\", code: \"pa\" },\r\n    { name: \"Assamese\", code: \"as\" },\r\n    { name: \"Maithili\", code: \"mai\" },\r\n    { name: \"Santali\", code: \"sat\" },\r\n    { name: \"Kashmiri\", code: \"ks\" },\r\n    { name: \"Konkani\", code: \"kok\" },\r\n    { name: \"Sindhi\", code: \"sd\" },\r\n    { name: \"Sanskrit\", code: \"sa\" },\r\n    { name: \"Manipuri\", code: \"mni\" },\r\n    { name: \"Bodo\", code: \"brx\" },\r\n    { name: \"Dogri\", code: \"doi\" },\r\n  ];\r\n  createForm!: FormGroup;\r\n\r\n  constructor() {\r\n    this.buildCreateTemplateForm();\r\n    this.buildVariableMappingForm();\r\n    this.buildAddLanguageForm();\r\n  }\r\n\r\n  buildCreateTemplateForm() {\r\n    this.createForm = this.fb.group({\r\n      channelType: [\"email\"],\r\n      templateName: [null, [Validators.required]],\r\n      activeLanguage: [this.allLanguages[0].code],\r\n      languages: this.buildLanguagesFormArray(),\r\n    });\r\n  }\r\n\r\n  buildLanguagesFormArray(data?: any[]) {\r\n    const formArray = new FormArray([]);\r\n    data = data || [this.allLanguages[0]];\r\n    data?.forEach((o) => {\r\n      formArray.push(this.buildLanguageFormGroup(o));\r\n    });\r\n    return formArray;\r\n  }\r\n\r\n  buildLanguageFormGroup(data?: any) {\r\n    return this.fb.group({\r\n      languageCode: data?.code,\r\n      languageName: data?.name,\r\n      emailSubject: [null],\r\n      templateBody: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  buildVariableMappingForm() {\r\n    this.variableMappingForm = this.fb.group({\r\n      databaseField: ['', [Validators.required]]\r\n    });\r\n  }\r\n\r\n  buildAddLanguageForm() {\r\n    this.addLanguageForm = this.fb.group({\r\n      searchTerm: ['']\r\n    });\r\n  }\r\n\r\n  get fValue(): any {\r\n    return this.createForm.value;\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('AfterViewInit - Modal template reference:', this.variableMappingModal);\r\n  }\r\n\r\n  onTemplateBodyClick(event: MouseEvent, languageIndex: number) {\r\n    console.log('Template body clicked!');\r\n    console.log('Channel type:', this.fValue?.channelType);\r\n\r\n    // Only handle clicks for SMS channel type\r\n    if (this.fValue?.channelType !== 'sms') {\r\n      console.log('Not SMS channel, ignoring click');\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLTextAreaElement;\r\n    const text = target.value || '';\r\n    const cursorPosition = target.selectionStart || 0;\r\n\r\n    console.log('Text:', text);\r\n    console.log('Cursor position:', cursorPosition);\r\n\r\n    // Find if cursor is within a [Var] placeholder\r\n    const varPattern = /\\[Var\\]/gi;\r\n    let match;\r\n\r\n    while ((match = varPattern.exec(text)) !== null) {\r\n      const start = match.index;\r\n      const end = match.index + match[0].length;\r\n\r\n      console.log(`Found [Var] at position ${start}-${end}, cursor at ${cursorPosition}`);\r\n\r\n      if (cursorPosition >= start && cursorPosition <= end) {\r\n        console.log('Cursor is within [Var], opening modal');\r\n        // Try modal first, fallback to simple prompt\r\n        try {\r\n          this.openVariableMappingModal(match[0], start, end, languageIndex);\r\n        } catch (error) {\r\n          console.error('Modal failed, using simple prompt:', error);\r\n          this.openSimpleVariableMapping(match[0], start, end, languageIndex);\r\n        }\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  openVariableMappingModal(variable: string, startPos: number, endPos: number, languageIndex: number) {\r\n    console.log('openVariableMappingModal called with:', variable);\r\n    console.log('Modal template reference:', this.variableMappingModal);\r\n    console.log('Modal service:', this.modalService);\r\n\r\n    // First, let's test with a simple alert\r\n    alert(`Variable mapping requested for: ${variable} at position ${startPos}-${endPos}`);\r\n\r\n    // Store current mapping context\r\n    this.currentVariable = variable;\r\n    this.currentStartPos = startPos;\r\n    this.currentEndPos = endPos;\r\n    this.currentLanguageIndex = languageIndex;\r\n\r\n    // Reset form\r\n    this.variableMappingForm.reset();\r\n\r\n    // Check if template reference exists\r\n    if (!this.variableMappingModal) {\r\n      console.error('Modal template reference not found!');\r\n      alert('Modal template not found. Please check the template reference.');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Open modal\r\n      console.log('Attempting to open modal...');\r\n      this.modalRef = this.modalService.show(this.variableMappingModal, {\r\n        class: 'modal-dialog-centered',\r\n        ignoreBackdropClick: true\r\n      });\r\n      console.log('Modal opened successfully:', this.modalRef);\r\n    } catch (error) {\r\n      console.error('Error opening modal:', error);\r\n      alert('Error opening modal: ' + error);\r\n    }\r\n  }\r\n\r\n  updateTemplateBodyWithMapping(mappedField: string, startPos: number, endPos: number, languageIndex: number) {\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n    const languageGroup = languagesArray.at(languageIndex);\r\n    const templateBodyControl = languageGroup.get('templateBody');\r\n\r\n    if (templateBodyControl) {\r\n      const currentText = templateBodyControl.value || '';\r\n      const newText = currentText.substring(0, startPos) +\r\n                     `[${mappedField}]` +\r\n                     currentText.substring(endPos);\r\n      templateBodyControl.setValue(newText);\r\n    }\r\n  }\r\n\r\n  getVariableCount(templateBody: string): number {\r\n    if (!templateBody) return 0;\r\n    const varPattern = /\\[Var\\]/gi;\r\n    const matches = templateBody.match(varPattern);\r\n    return matches ? matches.length : 0;\r\n  }\r\n\r\n  getMappedVariableCount(templateBody: string): number {\r\n    if (!templateBody) return 0;\r\n    // Count variables that are not [Var] (i.e., already mapped)\r\n    const allVarPattern = /\\[[^\\]]+\\]/g;\r\n    const unmappedVarPattern = /\\[Var\\]/gi;\r\n\r\n    const allMatches = templateBody.match(allVarPattern);\r\n    const unmappedMatches = templateBody.match(unmappedVarPattern);\r\n\r\n    const totalVars = allMatches ? allMatches.length : 0;\r\n    const unmappedVars = unmappedMatches ? unmappedMatches.length : 0;\r\n\r\n    return totalVars - unmappedVars;\r\n  }\r\n\r\n\r\n\r\n  onHeaderUpload(event: any) {\r\n    // Handle header upload for letter type\r\n  }\r\n\r\n  onFooterUpload(event: any) {\r\n    // Handle footer upload for letter type\r\n  }\r\n\r\n  // Template-based modal methods\r\n  useSuggestedField(field: string) {\r\n    this.variableMappingForm.patchValue({\r\n      databaseField: field\r\n    });\r\n  }\r\n\r\n  mapVariableFromTemplate() {\r\n    if (this.variableMappingForm.valid) {\r\n      const mappedField = this.variableMappingForm.get('databaseField')?.value;\r\n      this.updateTemplateBodyWithMapping(mappedField, this.currentStartPos, this.currentEndPos, this.currentLanguageIndex);\r\n      this.closeVariableModal();\r\n    }\r\n  }\r\n\r\n  closeVariableModal() {\r\n    this.modalRef?.hide();\r\n    this.variableMappingForm.reset();\r\n  }\r\n\r\n  // Alternative method using a simple prompt for testing\r\n  openSimpleVariableMapping(variable: string, startPos: number, endPos: number, languageIndex: number) {\r\n    const mappedField = prompt(`Map variable \"${variable}\" to database field:`, 'user.first_name');\r\n    if (mappedField) {\r\n      this.updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex);\r\n    }\r\n  }\r\n\r\n  // Add Language Modal Methods\r\n  openAddLanguageModal() {\r\n    // Reset form and selection\r\n    this.addLanguageForm.reset();\r\n    this.selectedLanguagesForAdd = [];\r\n\r\n    // Check if template reference exists\r\n    if (!this.addLanguageModal) {\r\n      alert('Add Language modal template not found.');\r\n      return;\r\n    }\r\n\r\n    // Open modal\r\n    this.modalRef = this.modalService.show(this.addLanguageModal, {\r\n      class: 'modal-dialog-centered modal-lg',\r\n      ignoreBackdropClick: true\r\n    });\r\n  }\r\n\r\n  closeAddLanguageModal() {\r\n    this.modalRef?.hide();\r\n    this.addLanguageForm.reset();\r\n    this.selectedLanguagesForAdd = [];\r\n  }\r\n\r\n  getFilteredAvailableLanguages() {\r\n    const searchTerm = this.addLanguageForm.get('searchTerm')?.value?.toLowerCase() || '';\r\n    const currentLanguageCodes = this.fValue?.languages?.map((lang: any) => lang.languageCode) || [];\r\n\r\n    return this.allLanguages.filter(lang =>\r\n      !currentLanguageCodes.includes(lang.code) &&\r\n      (lang.name.toLowerCase().includes(searchTerm) ||\r\n       lang.code.toLowerCase().includes(searchTerm))\r\n    );\r\n  }\r\n\r\n  isLanguageSelected(language: any): boolean {\r\n    return this.selectedLanguagesForAdd.some(lang => lang.code === language.code);\r\n  }\r\n\r\n  toggleLanguageSelection(language: any) {\r\n    const index = this.selectedLanguagesForAdd.findIndex(lang => lang.code === language.code);\r\n    if (index > -1) {\r\n      this.selectedLanguagesForAdd.splice(index, 1);\r\n    } else {\r\n      this.selectedLanguagesForAdd.push(language);\r\n    }\r\n  }\r\n\r\n  removeLanguageFromSelection(language: any) {\r\n    const index = this.selectedLanguagesForAdd.findIndex(lang => lang.code === language.code);\r\n    if (index > -1) {\r\n      this.selectedLanguagesForAdd.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  addSelectedLanguages() {\r\n    if (this.selectedLanguagesForAdd.length === 0) return;\r\n\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n\r\n    // Add each selected language to the form array\r\n    this.selectedLanguagesForAdd.forEach(language => {\r\n      const newLanguageGroup = this.buildLanguageFormGroup(language);\r\n      languagesArray.push(newLanguageGroup);\r\n    });\r\n\r\n    // Close modal\r\n    this.closeAddLanguageModal();\r\n  }\r\n\r\n  removeLanguage(index: number) {\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n\r\n    if (languagesArray.length <= 1) {\r\n      alert('At least one language is required.');\r\n      return;\r\n    }\r\n\r\n    const languageToRemove = this.fValue?.languages?.[index];\r\n    const confirmMessage = `Are you sure you want to remove \"${languageToRemove?.languageName}\" language? This will delete all content for this language.`;\r\n\r\n    if (confirm(confirmMessage)) {\r\n      languagesArray.removeAt(index);\r\n\r\n      // If the removed language was the active one, switch to the first available language\r\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\r\n      if (currentActiveLanguage === languageToRemove?.languageCode) {\r\n        const firstLanguage = this.fValue?.languages?.[0];\r\n        if (firstLanguage) {\r\n          this.createForm.patchValue({\r\n            activeLanguage: firstLanguage.languageCode\r\n          });\r\n        }\r\n      }\r\n\r\n\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,SAAS,QAAoC,eAAe;AACxF,SACEC,SAAS,EACTC,WAAW,EAEXC,WAAW,EACXC,UAAU,QACL,gBAAgB;AACvB,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,cAAc,QAAoB,qBAAqB;AASzD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EA8DlCC,YAAA;IA7DQ,KAAAC,EAAE,GAAgBV,MAAM,CAACG,WAAW,CAAC;IACrC,KAAAQ,YAAY,GAAmBX,MAAM,CAACO,cAAc,CAAC;IAQ7D,KAAAK,eAAe,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAW,CAAC;IAC3B,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,oBAAoB,GAAW,CAAC;IAIhC,KAAAC,uBAAuB,GAAU,EAAE;IAEnC;IACA,KAAAC,eAAe,GAAG,CAChB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,CACf;IAED,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IACD,KAAAC,YAAY,GAAU,CACpB;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,CAC/B;IAIC,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAF,uBAAuBA,CAAA;IACrB,IAAI,CAACG,UAAU,GAAG,IAAI,CAAChB,EAAE,CAACiB,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,OAAO,CAAC;MACtBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACxB,UAAU,CAACyB,QAAQ,CAAC,CAAC;MAC3CC,cAAc,EAAE,CAAC,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC;MAC3CU,SAAS,EAAE,IAAI,CAACC,uBAAuB;KACxC,CAAC;EACJ;EAEAA,uBAAuBA,CAACC,IAAY;IAClC,MAAMC,SAAS,GAAG,IAAIjC,SAAS,CAAC,EAAE,CAAC;IACnCgC,IAAI,GAAGA,IAAI,IAAI,CAAC,IAAI,CAACd,YAAY,CAAC,CAAC,CAAC,CAAC;IACrCc,IAAI,EAAEE,OAAO,CAAEC,CAAC,IAAI;MAClBF,SAAS,CAACG,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,OAAOF,SAAS;EAClB;EAEAI,sBAAsBA,CAACL,IAAU;IAC/B,OAAO,IAAI,CAACxB,EAAE,CAACiB,KAAK,CAAC;MACnBa,YAAY,EAAEN,IAAI,EAAEZ,IAAI;MACxBmB,YAAY,EAAEP,IAAI,EAAEb,IAAI;MACxBqB,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACtC,UAAU,CAACyB,QAAQ,CAAC;KAC3C,CAAC;EACJ;EAEAN,wBAAwBA,CAAA;IACtB,IAAI,CAACoB,mBAAmB,GAAG,IAAI,CAAClC,EAAE,CAACiB,KAAK,CAAC;MACvCkB,aAAa,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACyB,QAAQ,CAAC;KAC1C,CAAC;EACJ;EAEAL,oBAAoBA,CAAA;IAClB,IAAI,CAACqB,eAAe,GAAG,IAAI,CAACpC,EAAE,CAACiB,KAAK,CAAC;MACnCoB,UAAU,EAAE,CAAC,EAAE;KAChB,CAAC;EACJ;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACtB,UAAU,CAACuB,KAAK;EAC9B;EAEAC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACC,oBAAoB,CAAC;EACrF;EAEAC,mBAAmBA,CAACC,KAAiB,EAAEC,aAAqB;IAC1DL,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,MAAM,EAAEpB,WAAW,CAAC;IAEtD;IACA,IAAI,IAAI,CAACoB,MAAM,EAAEpB,WAAW,KAAK,KAAK,EAAE;MACtCuB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C;IACF;IAEA,MAAMK,MAAM,GAAGF,KAAK,CAACE,MAA6B;IAClD,MAAMC,IAAI,GAAGD,MAAM,CAACR,KAAK,IAAI,EAAE;IAC/B,MAAMU,cAAc,GAAGF,MAAM,CAACG,cAAc,IAAI,CAAC;IAEjDT,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEM,IAAI,CAAC;IAC1BP,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,cAAc,CAAC;IAE/C;IACA,MAAME,UAAU,GAAG,WAAW;IAC9B,IAAIC,KAAK;IAET,OAAO,CAACA,KAAK,GAAGD,UAAU,CAACE,IAAI,CAACL,IAAI,CAAC,MAAM,IAAI,EAAE;MAC/C,MAAMM,KAAK,GAAGF,KAAK,CAACG,KAAK;MACzB,MAAMC,GAAG,GAAGJ,KAAK,CAACG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACK,MAAM;MAEzChB,OAAO,CAACC,GAAG,CAAC,2BAA2BY,KAAK,IAAIE,GAAG,eAAeP,cAAc,EAAE,CAAC;MAEnF,IAAIA,cAAc,IAAIK,KAAK,IAAIL,cAAc,IAAIO,GAAG,EAAE;QACpDf,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA,IAAI;UACF,IAAI,CAACgB,wBAAwB,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEE,GAAG,EAAEV,aAAa,CAAC;QACpE,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdlB,OAAO,CAACkB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACC,yBAAyB,CAACR,KAAK,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEE,GAAG,EAAEV,aAAa,CAAC;QACrE;QACA;MACF;IACF;EACF;EAEAY,wBAAwBA,CAACG,QAAgB,EAAEC,QAAgB,EAAEC,MAAc,EAAEjB,aAAqB;IAChGL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEmB,QAAQ,CAAC;IAC9DpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACC,oBAAoB,CAAC;IACnEF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzC,YAAY,CAAC;IAEhD;IACA+D,KAAK,CAAC,mCAAmCH,QAAQ,gBAAgBC,QAAQ,IAAIC,MAAM,EAAE,CAAC;IAEtF;IACA,IAAI,CAAC7D,eAAe,GAAG2D,QAAQ;IAC/B,IAAI,CAAC1D,eAAe,GAAG2D,QAAQ;IAC/B,IAAI,CAAC1D,aAAa,GAAG2D,MAAM;IAC3B,IAAI,CAAC1D,oBAAoB,GAAGyC,aAAa;IAEzC;IACA,IAAI,CAACZ,mBAAmB,CAAC+B,KAAK,EAAE;IAEhC;IACA,IAAI,CAAC,IAAI,CAACtB,oBAAoB,EAAE;MAC9BF,OAAO,CAACkB,KAAK,CAAC,qCAAqC,CAAC;MACpDK,KAAK,CAAC,gEAAgE,CAAC;MACvE;IACF;IAEA,IAAI;MACF;MACAvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACwB,QAAQ,GAAG,IAAI,CAACjE,YAAY,CAACkE,IAAI,CAAC,IAAI,CAACxB,oBAAoB,EAAE;QAChEyB,KAAK,EAAE,uBAAuB;QAC9BC,mBAAmB,EAAE;OACtB,CAAC;MACF5B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwB,QAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CK,KAAK,CAAC,uBAAuB,GAAGL,KAAK,CAAC;IACxC;EACF;EAEAW,6BAA6BA,CAACC,WAAmB,EAAET,QAAgB,EAAEC,MAAc,EAAEjB,aAAqB;IACxG,MAAM0B,cAAc,GAAG,IAAI,CAACxD,UAAU,CAACyD,GAAG,CAAC,WAAW,CAAc;IACpE,MAAMC,aAAa,GAAGF,cAAc,CAACG,EAAE,CAAC7B,aAAa,CAAC;IACtD,MAAM8B,mBAAmB,GAAGF,aAAa,CAACD,GAAG,CAAC,cAAc,CAAC;IAE7D,IAAIG,mBAAmB,EAAE;MACvB,MAAMC,WAAW,GAAGD,mBAAmB,CAACrC,KAAK,IAAI,EAAE;MACnD,MAAMuC,OAAO,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAEjB,QAAQ,CAAC,GACnC,IAAIS,WAAW,GAAG,GAClBM,WAAW,CAACE,SAAS,CAAChB,MAAM,CAAC;MAC5Ca,mBAAmB,CAACI,QAAQ,CAACF,OAAO,CAAC;IACvC;EACF;EAEAG,gBAAgBA,CAAChD,YAAoB;IACnC,IAAI,CAACA,YAAY,EAAE,OAAO,CAAC;IAC3B,MAAMkB,UAAU,GAAG,WAAW;IAC9B,MAAM+B,OAAO,GAAGjD,YAAY,CAACmB,KAAK,CAACD,UAAU,CAAC;IAC9C,OAAO+B,OAAO,GAAGA,OAAO,CAACzB,MAAM,GAAG,CAAC;EACrC;EAEA0B,sBAAsBA,CAAClD,YAAoB;IACzC,IAAI,CAACA,YAAY,EAAE,OAAO,CAAC;IAC3B;IACA,MAAMmD,aAAa,GAAG,aAAa;IACnC,MAAMC,kBAAkB,GAAG,WAAW;IAEtC,MAAMC,UAAU,GAAGrD,YAAY,CAACmB,KAAK,CAACgC,aAAa,CAAC;IACpD,MAAMG,eAAe,GAAGtD,YAAY,CAACmB,KAAK,CAACiC,kBAAkB,CAAC;IAE9D,MAAMG,SAAS,GAAGF,UAAU,GAAGA,UAAU,CAAC7B,MAAM,GAAG,CAAC;IACpD,MAAMgC,YAAY,GAAGF,eAAe,GAAGA,eAAe,CAAC9B,MAAM,GAAG,CAAC;IAEjE,OAAO+B,SAAS,GAAGC,YAAY;EACjC;EAIAC,cAAcA,CAAC7C,KAAU;IACvB;EAAA;EAGF8C,cAAcA,CAAC9C,KAAU;IACvB;EAAA;EAGF;EACA+C,iBAAiBA,CAACC,KAAa;IAC7B,IAAI,CAAC3D,mBAAmB,CAAC4D,UAAU,CAAC;MAClC3D,aAAa,EAAE0D;KAChB,CAAC;EACJ;EAEAE,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAAC7D,mBAAmB,CAAC8D,KAAK,EAAE;MAClC,MAAMzB,WAAW,GAAG,IAAI,CAACrC,mBAAmB,CAACuC,GAAG,CAAC,eAAe,CAAC,EAAElC,KAAK;MACxE,IAAI,CAAC+B,6BAA6B,CAACC,WAAW,EAAE,IAAI,CAACpE,eAAe,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAACC,oBAAoB,CAAC;MACpH,IAAI,CAAC4F,kBAAkB,EAAE;IAC3B;EACF;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAAC/B,QAAQ,EAAEgC,IAAI,EAAE;IACrB,IAAI,CAAChE,mBAAmB,CAAC+B,KAAK,EAAE;EAClC;EAEA;EACAL,yBAAyBA,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,MAAc,EAAEjB,aAAqB;IACjG,MAAMyB,WAAW,GAAG4B,MAAM,CAAC,iBAAiBtC,QAAQ,sBAAsB,EAAE,iBAAiB,CAAC;IAC9F,IAAIU,WAAW,EAAE;MACf,IAAI,CAACD,6BAA6B,CAACC,WAAW,EAAET,QAAQ,EAAEC,MAAM,EAAEjB,aAAa,CAAC;IAClF;EACF;EAEA;EACAsD,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAChE,eAAe,CAAC6B,KAAK,EAAE;IAC5B,IAAI,CAAC3D,uBAAuB,GAAG,EAAE;IAEjC;IACA,IAAI,CAAC,IAAI,CAAC+F,gBAAgB,EAAE;MAC1BrC,KAAK,CAAC,wCAAwC,CAAC;MAC/C;IACF;IAEA;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACjE,YAAY,CAACkE,IAAI,CAAC,IAAI,CAACkC,gBAAgB,EAAE;MAC5DjC,KAAK,EAAE,gCAAgC;MACvCC,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEAiC,qBAAqBA,CAAA;IACnB,IAAI,CAACpC,QAAQ,EAAEgC,IAAI,EAAE;IACrB,IAAI,CAAC9D,eAAe,CAAC6B,KAAK,EAAE;IAC5B,IAAI,CAAC3D,uBAAuB,GAAG,EAAE;EACnC;EAEAiG,6BAA6BA,CAAA;IAC3B,MAAMlE,UAAU,GAAG,IAAI,CAACD,eAAe,CAACqC,GAAG,CAAC,YAAY,CAAC,EAAElC,KAAK,EAAEiE,WAAW,EAAE,IAAI,EAAE;IACrF,MAAMC,oBAAoB,GAAG,IAAI,CAACnE,MAAM,EAAEhB,SAAS,EAAEoF,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC7E,YAAY,CAAC,IAAI,EAAE;IAEhG,OAAO,IAAI,CAACpB,YAAY,CAACkG,MAAM,CAACD,IAAI,IAClC,CAACF,oBAAoB,CAACI,QAAQ,CAACF,IAAI,CAAC/F,IAAI,CAAC,KACxC+F,IAAI,CAAChG,IAAI,CAAC6F,WAAW,EAAE,CAACK,QAAQ,CAACxE,UAAU,CAAC,IAC5CsE,IAAI,CAAC/F,IAAI,CAAC4F,WAAW,EAAE,CAACK,QAAQ,CAACxE,UAAU,CAAC,CAAC,CAC/C;EACH;EAEAyE,kBAAkBA,CAACC,QAAa;IAC9B,OAAO,IAAI,CAACzG,uBAAuB,CAAC0G,IAAI,CAACL,IAAI,IAAIA,IAAI,CAAC/F,IAAI,KAAKmG,QAAQ,CAACnG,IAAI,CAAC;EAC/E;EAEAqG,uBAAuBA,CAACF,QAAa;IACnC,MAAMxD,KAAK,GAAG,IAAI,CAACjD,uBAAuB,CAAC4G,SAAS,CAACP,IAAI,IAAIA,IAAI,CAAC/F,IAAI,KAAKmG,QAAQ,CAACnG,IAAI,CAAC;IACzF,IAAI2C,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjD,uBAAuB,CAAC6G,MAAM,CAAC5D,KAAK,EAAE,CAAC,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACjD,uBAAuB,CAACsB,IAAI,CAACmF,QAAQ,CAAC;IAC7C;EACF;EAEAK,2BAA2BA,CAACL,QAAa;IACvC,MAAMxD,KAAK,GAAG,IAAI,CAACjD,uBAAuB,CAAC4G,SAAS,CAACP,IAAI,IAAIA,IAAI,CAAC/F,IAAI,KAAKmG,QAAQ,CAACnG,IAAI,CAAC;IACzF,IAAI2C,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjD,uBAAuB,CAAC6G,MAAM,CAAC5D,KAAK,EAAE,CAAC,CAAC;IAC/C;EACF;EAEA8D,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC/G,uBAAuB,CAACmD,MAAM,KAAK,CAAC,EAAE;IAE/C,MAAMe,cAAc,GAAG,IAAI,CAACxD,UAAU,CAACyD,GAAG,CAAC,WAAW,CAAc;IAEpE;IACA,IAAI,CAACnE,uBAAuB,CAACoB,OAAO,CAACqF,QAAQ,IAAG;MAC9C,MAAMO,gBAAgB,GAAG,IAAI,CAACzF,sBAAsB,CAACkF,QAAQ,CAAC;MAC9DvC,cAAc,CAAC5C,IAAI,CAAC0F,gBAAgB,CAAC;IACvC,CAAC,CAAC;IAEF;IACA,IAAI,CAAChB,qBAAqB,EAAE;EAC9B;EAEAiB,cAAcA,CAAChE,KAAa;IAC1B,MAAMiB,cAAc,GAAG,IAAI,CAACxD,UAAU,CAACyD,GAAG,CAAC,WAAW,CAAc;IAEpE,IAAID,cAAc,CAACf,MAAM,IAAI,CAAC,EAAE;MAC9BO,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,MAAMwD,gBAAgB,GAAG,IAAI,CAAClF,MAAM,EAAEhB,SAAS,GAAGiC,KAAK,CAAC;IACxD,MAAMkE,cAAc,GAAG,oCAAoCD,gBAAgB,EAAEzF,YAAY,6DAA6D;IAEtJ,IAAI2F,OAAO,CAACD,cAAc,CAAC,EAAE;MAC3BjD,cAAc,CAACmD,QAAQ,CAACpE,KAAK,CAAC;MAE9B;MACA,MAAMqE,qBAAqB,GAAG,IAAI,CAAC5G,UAAU,CAACyD,GAAG,CAAC,gBAAgB,CAAC,EAAElC,KAAK;MAC1E,IAAIqF,qBAAqB,KAAKJ,gBAAgB,EAAE1F,YAAY,EAAE;QAC5D,MAAM+F,aAAa,GAAG,IAAI,CAACvF,MAAM,EAAEhB,SAAS,GAAG,CAAC,CAAC;QACjD,IAAIuG,aAAa,EAAE;UACjB,IAAI,CAAC7G,UAAU,CAAC8E,UAAU,CAAC;YACzBzE,cAAc,EAAEwG,aAAa,CAAC/F;WAC/B,CAAC;QACJ;MACF;IAGF;EACF;;;;;;;cAzWCvC,SAAS;QAAAuI,IAAA,GAAC,sBAAsB;MAAA;;cAChCvI,SAAS;QAAAuI,IAAA,GAAC,kBAAkB;MAAA;;;;AANlBhI,uBAAuB,GAAAiI,UAAA,EAPnC1I,SAAS,CAAC;EACT2I,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxI,WAAW,EAAEE,YAAY,CAAC;EACpCuI,QAAA,EAAAC,oBAA+C;;CAEhD,CAAC,C,EACWtI,uBAAuB,CA+WnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}