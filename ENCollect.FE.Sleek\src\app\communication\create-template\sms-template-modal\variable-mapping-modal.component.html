<div class="modal-header">
  <h4 class="modal-title">Map Variable to Database Field</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <p class="text-muted mb-3">Specify which database field this variable should use</p>
  
  <form [formGroup]="mappingForm">
    <div class="form-control-group">
      <label class="form-label required">Database Field</label>
      <input
        type="text"
        class="form-control"
        placeholder="e.g., user.first_name"
        formControlName="databaseField"
        autocomplete="off"
      />
      <div class="invalid-feedback" *ngIf="mappingForm.get('databaseField')?.invalid && mappingForm.get('databaseField')?.touched">
        Database field is required
      </div>
    </div>

    <!-- Suggested Fields -->
    <div class="suggested-fields mt-3">
      <label class="form-label">Suggested Fields:</label>
      <div class="suggested-fields-container">
        <button
          type="button"
          class="btn btn-outline-secondary btn-sm me-2 mb-2"
          *ngFor="let field of suggestedFields"
          (click)="useSuggestedField(field)"
        >
          {{ field }}
        </button>
      </div>
    </div>
  </form>

  <!-- Preview section -->
  <div class="mt-3 p-3 bg-light rounded">
    <small class="text-muted">Preview:</small>
    <div class="mt-1">
      <span class="text-success me-2">{{ variable }}</span>
      <span class="text-muted">→</span>
      <span class="text-primary ms-2" *ngIf="mappingForm.get('databaseField')?.value">
        [{{ mappingForm.get('databaseField')?.value }}]
      </span>
      <span class="text-muted ms-2" *ngIf="!mappingForm.get('databaseField')?.value">
        [database_field]
      </span>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button 
    type="button" 
    class="btn btn-secondary me-2" 
    (click)="mapVariable()"
    [disabled]="mappingForm.invalid"
  >
    Map
  </button>
  <button type="button" class="btn btn-outline-secondary" (click)="closeModal()">
    Cancel
  </button>
</div>
