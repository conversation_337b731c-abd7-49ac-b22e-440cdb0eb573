import { Component, Input } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-variable-mapping-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './variable-mapping-modal.component.html',
  styleUrls: ['./variable-mapping-modal.component.scss']
})
export class VariableMappingModalComponent {
  @Input() variable: string = '';
  @Input() onMap?: (mappedField: string) => void;

  mappingForm: FormGroup;

  // Sample database field suggestions
  suggestedFields = [
    'user.first_name',
    'user.last_name',
    'user.email',
    'user.phone',
    'account.number',
    'account.balance',
    'payment.amount',
    'payment.due_date',
    'company.name'
  ];

  constructor(
    public bsModalRef: BsModalRef,
    private fb: FormBuilder
  ) {
    this.mappingForm = this.fb.group({
      databaseField: ['', [Validators.required]]
    });
  }

  mapVariable() {
    if (this.mappingForm.valid) {
      const mappedField = this.mappingForm.get('databaseField')?.value;
      if (this.onMap) {
        this.onMap(mappedField);
      }
    }
  }

  useSuggestedField(field: string) {
    this.mappingForm.patchValue({
      databaseField: field
    });
  }

  closeModal() {
    this.bsModalRef.hide();
  }
}
