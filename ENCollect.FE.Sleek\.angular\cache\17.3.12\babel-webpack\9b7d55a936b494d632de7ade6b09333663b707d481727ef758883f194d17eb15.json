{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { RouterModule } from \"@angular/router\";\nimport { AcmGuard, ACMP, AuthGuard } from \"../../shared\";\nimport { CreateComponent } from \"./create-template/create.component\";\nimport { EditComponent } from \"./view-edit-template/edit.component\";\nimport { SearchComponent } from \"./search-template/search.component\";\nimport { createTaskComponent } from \"./createTask/createTask.component\";\nimport { searchTaskComponent } from \"./searchTask/searchTask.component\";\nimport { uploadLetterStatusComponent } from \"./uploadLetterStatus/uploadLetterStatus.component\";\nimport { viewUploadLetterStatusComponent } from \"./viewUploadLetterStatus/viewUploadLetterStatus.component\";\nimport { ViewEditTaskComponent } from \"./view-edit-task/view-edit-task.component\";\nimport { BroadcastmessageComponent } from \"./broadcastmessage/broadcastmessage.component\";\nimport { CustomerProfileComponent } from \"./customer-profile/customer-profile.component\";\nconst appRoutes = [{\n  path: \"\",\n  children: [{\n    path: \"create-template\",\n    component: CreateComponent,\n    canActivate: [AuthGuard, AcmGuard]\n    // data: {\n    //   acm: [ACMP.CanCreateCommunicationTemplate],\n    // },\n  }, {\n    path: \"search-template\",\n    component: SearchComponent,\n    canActivate: [AuthGuard, AcmGuard]\n    // data: {\n    //   acm: [ACMP.CanSearchCommunicationTemplate],\n    // },\n  }, {\n    path: \"edit-communication-template/:id\",\n    component: EditComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: [ACMP.CanUpdateCommunicationTemplate]\n    }\n  }, {\n    path: \"view-communication-template/:id\",\n    component: EditComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: [ACMP.CanViewCommunicationTemplate]\n    }\n  }, {\n    path: \"create-task\",\n    component: createTaskComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"search-task\",\n    component: searchTaskComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"view-task/:id\",\n    component: ViewEditTaskComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"edit-task/:id\",\n    component: ViewEditTaskComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"Upload-letter-status\",\n    component: uploadLetterStatusComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"view-Upload-letter-status\",\n    component: viewUploadLetterStatusComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"broadcast\",\n    component: BroadcastmessageComponent,\n    canActivate: [AuthGuard, AcmGuard],\n    data: {\n      acm: []\n    }\n  }, {\n    path: \"customer-profile-update\",\n    component: CustomerProfileComponent,\n    pathMatch: \"full\"\n  }]\n}];\nlet TemplateRoutingModule = class TemplateRoutingModule {};\nTemplateRoutingModule = __decorate([NgModule({\n  imports: [RouterModule.forChild(appRoutes)],\n  exports: [RouterModule]\n})], TemplateRoutingModule);\nexport { TemplateRoutingModule };", "map": {"version": 3, "names": ["NgModule", "RouterModule", "AcmGuard", "ACMP", "<PERSON><PERSON><PERSON><PERSON>", "CreateComponent", "EditComponent", "SearchComponent", "createTaskComponent", "searchTaskComponent", "uploadLetterStatusComponent", "viewUploadLetterStatusComponent", "ViewEditTaskComponent", "BroadcastmessageComponent", "CustomerProfileComponent", "appRoutes", "path", "children", "component", "canActivate", "data", "acm", "CanUpdateCommunicationTemplate", "CanViewCommunicationTemplate", "pathMatch", "TemplateRoutingModule", "__decorate", "imports", "<PERSON><PERSON><PERSON><PERSON>", "exports"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\template\\template-routing.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule, Routes } from \"@angular/router\";\r\n\r\nimport {AcmGuard, ACMP, AuthGuard } from \"../../shared\";\r\n\r\nimport { CreateComponent } from \"./create-template/create.component\";\r\nimport { EditComponent } from \"./view-edit-template/edit.component\";\r\nimport { SearchComponent } from \"./search-template/search.component\";\r\nimport { createTaskComponent } from \"./createTask/createTask.component\";\r\nimport { searchTaskComponent } from \"./searchTask/searchTask.component\";\r\nimport { uploadLetterStatusComponent } from \"./uploadLetterStatus/uploadLetterStatus.component\";\r\nimport { viewUploadLetterStatusComponent } from \"./viewUploadLetterStatus/viewUploadLetterStatus.component\";\r\nimport { ViewEditTaskComponent } from \"./view-edit-task/view-edit-task.component\";\r\nimport { BroadcastmessageComponent } from \"./broadcastmessage/broadcastmessage.component\";\r\nimport { CustomerProfileComponent } from \"./customer-profile/customer-profile.component\";\r\n\r\nconst appRoutes: Routes = [\r\n  {\r\n    path: \"\",\r\n    children: [\r\n      {\r\n        path: \"create-template\",\r\n        component: CreateComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        // data: {\r\n        //   acm: [ACMP.CanCreateCommunicationTemplate],\r\n        // },\r\n      },\r\n      {\r\n        path: \"search-template\",\r\n        component: SearchComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        // data: {\r\n        //   acm: [ACMP.CanSearchCommunicationTemplate],\r\n        // },\r\n      },\r\n      {\r\n        path: \"edit-communication-template/:id\",\r\n        component: EditComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [ACMP.CanUpdateCommunicationTemplate],\r\n        },\r\n      },\r\n      {\r\n        path: \"view-communication-template/:id\",\r\n        component: EditComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [ACMP.CanViewCommunicationTemplate],\r\n        },\r\n      },\r\n      {\r\n        path: \"create-task\",\r\n        component: createTaskComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"search-task\",\r\n        component: searchTaskComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"view-task/:id\",\r\n        component: ViewEditTaskComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"edit-task/:id\",\r\n        component: ViewEditTaskComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"Upload-letter-status\",\r\n        component: uploadLetterStatusComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"view-Upload-letter-status\",\r\n        component: viewUploadLetterStatusComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"broadcast\",\r\n        component: BroadcastmessageComponent,\r\n        canActivate: [AuthGuard, AcmGuard],\r\n        data: {\r\n          acm: [],\r\n        },\r\n      },\r\n      {\r\n        path: \"customer-profile-update\",\r\n        component: CustomerProfileComponent,\r\n        pathMatch: \"full\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(appRoutes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class TemplateRoutingModule {}\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAgB,iBAAiB;AAEtD,SAAQC,QAAQ,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAEvD,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,2BAA2B,QAAQ,mDAAmD;AAC/F,SAASC,+BAA+B,QAAQ,2DAA2D;AAC3G,SAASC,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,yBAAyB,QAAQ,+CAA+C;AACzF,SAASC,wBAAwB,QAAQ,+CAA+C;AAExF,MAAMC,SAAS,GAAW,CACxB;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,iBAAiB;IACvBE,SAAS,EAAEb,eAAe;IAC1Bc,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ;IACjC;IACA;IACA;GACD,EACD;IACEc,IAAI,EAAE,iBAAiB;IACvBE,SAAS,EAAEX,eAAe;IAC1BY,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ;IACjC;IACA;IACA;GACD,EACD;IACEc,IAAI,EAAE,iCAAiC;IACvCE,SAAS,EAAEZ,aAAa;IACxBa,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE,CAAClB,IAAI,CAACmB,8BAA8B;;GAE5C,EACD;IACEN,IAAI,EAAE,iCAAiC;IACvCE,SAAS,EAAEZ,aAAa;IACxBa,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE,CAAClB,IAAI,CAACoB,4BAA4B;;GAE1C,EACD;IACEP,IAAI,EAAE,aAAa;IACnBE,SAAS,EAAEV,mBAAmB;IAC9BW,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,aAAa;IACnBE,SAAS,EAAET,mBAAmB;IAC9BU,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,eAAe;IACrBE,SAAS,EAAEN,qBAAqB;IAChCO,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,eAAe;IACrBE,SAAS,EAAEN,qBAAqB;IAChCO,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BE,SAAS,EAAER,2BAA2B;IACtCS,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,2BAA2B;IACjCE,SAAS,EAAEP,+BAA+B;IAC1CQ,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,WAAW;IACjBE,SAAS,EAAEL,yBAAyB;IACpCM,WAAW,EAAE,CAACf,SAAS,EAAEF,QAAQ,CAAC;IAClCkB,IAAI,EAAE;MACJC,GAAG,EAAE;;GAER,EACD;IACEL,IAAI,EAAE,yBAAyB;IAC/BE,SAAS,EAAEJ,wBAAwB;IACnCU,SAAS,EAAE;GACZ;CAEJ,CACF;AAMM,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB,GAAG;AAAxBA,qBAAqB,GAAAC,UAAA,EAJjC1B,QAAQ,CAAC;EACR2B,OAAO,EAAE,CAAC1B,YAAY,CAAC2B,QAAQ,CAACb,SAAS,CAAC,CAAC;EAC3Cc,OAAO,EAAE,CAAC5B,YAAY;CACvB,CAAC,C,EACWwB,qBAAqB,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}