{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./variable-mapping-modal.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./variable-mapping-modal.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { BsModalRef } from 'ngx-bootstrap/modal';\nimport { FormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nlet VariableMappingModalComponent = class VariableMappingModalComponent {\n  constructor(bsModalRef, fb) {\n    this.bsModalRef = bsModalRef;\n    this.fb = fb;\n    this.variable = '';\n    // Sample database field suggestions\n    this.suggestedFields = ['user.first_name', 'user.last_name', 'user.email', 'user.phone', 'account.number', 'account.balance', 'payment.amount', 'payment.due_date', 'company.name'];\n    this.mappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n  mapVariable() {\n    if (this.mappingForm.valid) {\n      const mappedField = this.mappingForm.get('databaseField')?.value;\n      if (this.onMap) {\n        this.onMap(mappedField);\n      }\n    }\n  }\n  useSuggestedField(field) {\n    this.mappingForm.patchValue({\n      databaseField: field\n    });\n  }\n  closeModal() {\n    this.bsModalRef.hide();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: BsModalRef\n    }, {\n      type: FormBuilder\n    }];\n  }\n  static {\n    this.propDecorators = {\n      variable: [{\n        type: Input\n      }],\n      onMap: [{\n        type: Input\n      }]\n    };\n  }\n};\nVariableMappingModalComponent = __decorate([Component({\n  selector: 'app-variable-mapping-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], VariableMappingModalComponent);\nexport { VariableMappingModalComponent };", "map": {"version": 3, "names": ["Component", "Input", "BsModalRef", "FormBuilder", "Validators", "ReactiveFormsModule", "CommonModule", "VariableMappingModalComponent", "constructor", "bsModalRef", "fb", "variable", "<PERSON><PERSON><PERSON><PERSON>", "mappingForm", "group", "databaseField", "required", "mapVariable", "valid", "mappedField", "get", "value", "onMap", "useSuggestedField", "field", "patchValue", "closeModal", "hide", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\sms-template-modal\\variable-mapping-modal.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { BsModalRef } from 'ngx-bootstrap/modal';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-variable-mapping-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  templateUrl: './variable-mapping-modal.component.html',\n  styleUrls: ['./variable-mapping-modal.component.scss']\n})\nexport class VariableMappingModalComponent {\n  @Input() variable: string = '';\n  @Input() onMap?: (mappedField: string) => void;\n\n  mappingForm: FormGroup;\n\n  // Sample database field suggestions\n  suggestedFields = [\n    'user.first_name',\n    'user.last_name',\n    'user.email',\n    'user.phone',\n    'account.number',\n    'account.balance',\n    'payment.amount',\n    'payment.due_date',\n    'company.name'\n  ];\n\n  constructor(\n    public bsModalRef: BsModalRef,\n    private fb: FormBuilder\n  ) {\n    this.mappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n\n  mapVariable() {\n    if (this.mappingForm.valid) {\n      const mappedField = this.mappingForm.get('databaseField')?.value;\n      if (this.onMap) {\n        this.onMap(mappedField);\n      }\n    }\n  }\n\n  useSuggestedField(field: string) {\n    this.mappingForm.patchValue({\n      databaseField: field\n    });\n  }\n\n  closeModal() {\n    this.bsModalRef.hide();\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,EAAaC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxF,SAASC,YAAY,QAAQ,iBAAiB;AASvC,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EAmBxCC,YACSC,UAAsB,EACrBC,EAAe;IADhB,KAAAD,UAAU,GAAVA,UAAU;IACT,KAAAC,EAAE,GAAFA,EAAE;IApBH,KAAAC,QAAQ,GAAW,EAAE;IAK9B;IACA,KAAAC,eAAe,GAAG,CAChB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,CACf;IAMC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MAC/BC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAACY,QAAQ,CAAC;KAC1C,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACJ,WAAW,CAACK,KAAK,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK;MAChE,IAAI,IAAI,CAACC,KAAK,EAAE;QACd,IAAI,CAACA,KAAK,CAACH,WAAW,CAAC;MACzB;IACF;EACF;EAEAI,iBAAiBA,CAACC,KAAa;IAC7B,IAAI,CAACX,WAAW,CAACY,UAAU,CAAC;MAC1BV,aAAa,EAAES;KAChB,CAAC;EACJ;EAEAE,UAAUA,CAAA;IACR,IAAI,CAACjB,UAAU,CAACkB,IAAI,EAAE;EACxB;;;;;;;;;;;cA5CC1B;MAAK;;cACLA;MAAK;;;;AAFKM,6BAA6B,GAAAqB,UAAA,EAPzC5B,SAAS,CAAC;EACT6B,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACzB,YAAY,EAAED,mBAAmB,CAAC;EAC5C2B,QAAA,EAAAC,oBAAsD;;CAEvD,CAAC,C,EACW1B,6BAA6B,CA8CzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}