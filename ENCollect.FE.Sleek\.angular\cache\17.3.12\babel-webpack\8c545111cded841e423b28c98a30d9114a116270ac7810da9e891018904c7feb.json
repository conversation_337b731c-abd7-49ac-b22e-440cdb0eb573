{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./variable-mapping-modal.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./variable-mapping-modal.component.scss?ngResource\";\nimport { Component, Input } from '@angular/core';\nimport { BsModalRef } from 'ngx-bootstrap/modal';\nimport { FormBuilder, Validators } from '@angular/forms';\nlet VariableMappingModalComponent = class VariableMappingModalComponent {\n  constructor(bsModalRef, fb) {\n    this.bsModalRef = bsModalRef;\n    this.fb = fb;\n    this.variable = '';\n    this.mappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n  mapVariable() {\n    if (this.mappingForm.valid) {\n      const mappedField = this.mappingForm.get('databaseField')?.value;\n      if (this.onMap) {\n        this.onMap(mappedField);\n      }\n    }\n  }\n  closeModal() {\n    this.bsModalRef.hide();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: BsModalRef\n    }, {\n      type: FormBuilder\n    }];\n  }\n  static {\n    this.propDecorators = {\n      variable: [{\n        type: Input\n      }],\n      onMap: [{\n        type: Input\n      }]\n    };\n  }\n};\nVariableMappingModalComponent = __decorate([Component({\n  selector: 'app-variable-mapping-modal',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], VariableMappingModalComponent);\nexport { VariableMappingModalComponent };", "map": {"version": 3, "names": ["Component", "Input", "BsModalRef", "FormBuilder", "Validators", "VariableMappingModalComponent", "constructor", "bsModalRef", "fb", "variable", "mappingForm", "group", "databaseField", "required", "mapVariable", "valid", "mappedField", "get", "value", "onMap", "closeModal", "hide", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\sms-template-modal\\variable-mapping-modal.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { BsModalRef } from 'ngx-bootstrap/modal';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-variable-mapping-modal',\n  templateUrl: './variable-mapping-modal.component.html',\n  styleUrls: ['./variable-mapping-modal.component.scss']\n})\nexport class VariableMappingModalComponent {\n  @Input() variable: string = '';\n  @Input() onMap?: (mappedField: string) => void;\n\n  mappingForm: FormGroup;\n\n  constructor(\n    public bsModalRef: BsModalRef,\n    private fb: FormBuilder\n  ) {\n    this.mappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n\n  mapVariable() {\n    if (this.mappingForm.valid) {\n      const mappedField = this.mappingForm.get('databaseField')?.value;\n      if (this.onMap) {\n        this.onMap(mappedField);\n      }\n    }\n  }\n\n  closeModal() {\n    this.bsModalRef.hide();\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAO5D,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EAMxCC,YACSC,UAAsB,EACrBC,EAAe;IADhB,KAAAD,UAAU,GAAVA,UAAU;IACT,KAAAC,EAAE,GAAFA,EAAE;IAPH,KAAAC,QAAQ,GAAW,EAAE;IAS5B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MAC/BC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACR,UAAU,CAACS,QAAQ,CAAC;KAC1C,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACJ,WAAW,CAACK,KAAK,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACN,WAAW,CAACO,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK;MAChE,IAAI,IAAI,CAACC,KAAK,EAAE;QACd,IAAI,CAACA,KAAK,CAACH,WAAW,CAAC;MACzB;IACF;EACF;EAEAI,UAAUA,CAAA;IACR,IAAI,CAACb,UAAU,CAACc,IAAI,EAAE;EACxB;;;;;;;;;;;cAzBCpB;MAAK;;cACLA;MAAK;;;;AAFKI,6BAA6B,GAAAiB,UAAA,EALzCtB,SAAS,CAAC;EACTuB,QAAQ,EAAE,4BAA4B;EACtCC,QAAA,EAAAC,oBAAsD;;CAEvD,CAAC,C,EACWpB,6BAA6B,CA2BzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}