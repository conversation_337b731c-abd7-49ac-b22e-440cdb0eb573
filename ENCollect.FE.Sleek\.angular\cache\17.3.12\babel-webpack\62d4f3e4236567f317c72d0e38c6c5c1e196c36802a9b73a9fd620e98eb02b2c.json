{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormsModule, Validators } from \"@angular/forms\";\nimport { SharedModule } from \"src/app/shared\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.currentVariable = '';\n    this.currentStartPos = 0;\n    this.currentEndPos = 0;\n    this.currentLanguageIndex = 0;\n    // Sample database field suggestions\n    this.suggestedFields = ['user.first_name', 'user.last_name', 'user.email', 'user.phone', 'account.number', 'account.balance', 'payment.amount', 'payment.due_date', 'company.name'];\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.allLanguages = [{\n      name: \"English\",\n      code: \"en\"\n    }, {\n      name: \"Hindi\",\n      code: \"hi\"\n    }, {\n      name: \"Tamil\",\n      code: \"ta\"\n    }, {\n      name: \"Bengali\",\n      code: \"bn\"\n    }, {\n      name: \"Telugu\",\n      code: \"te\"\n    }, {\n      name: \"Marathi\",\n      code: \"mr\"\n    }, {\n      name: \"Urdu\",\n      code: \"ur\"\n    }, {\n      name: \"Gujarati\",\n      code: \"gu\"\n    }, {\n      name: \"Kannada\",\n      code: \"kn\"\n    }, {\n      name: \"Malayalam\",\n      code: \"ml\"\n    }, {\n      name: \"Odia\",\n      code: \"or\"\n    }, {\n      name: \"Punjabi\",\n      code: \"pa\"\n    }, {\n      name: \"Assamese\",\n      code: \"as\"\n    }, {\n      name: \"Maithili\",\n      code: \"mai\"\n    }, {\n      name: \"Santali\",\n      code: \"sat\"\n    }, {\n      name: \"Kashmiri\",\n      code: \"ks\"\n    }, {\n      name: \"Konkani\",\n      code: \"kok\"\n    }, {\n      name: \"Sindhi\",\n      code: \"sd\"\n    }, {\n      name: \"Sanskrit\",\n      code: \"sa\"\n    }, {\n      name: \"Manipuri\",\n      code: \"mni\"\n    }, {\n      name: \"Bodo\",\n      code: \"brx\"\n    }, {\n      name: \"Dogri\",\n      code: \"doi\"\n    }];\n    this.buildCreateTemplateForm();\n    this.buildVariableMappingForm();\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\"],\n      templateName: [null, [Validators.required]],\n      activeLanguage: [this.allLanguages[0].code],\n      languages: this.buildLanguagesFormArray()\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    data = data || [this.allLanguages[0]];\n    data?.forEach(o => {\n      formArray.push(this.buildLanguageFormGroup(o));\n    });\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]]\n    });\n  }\n  buildVariableMappingForm() {\n    this.variableMappingForm = this.fb.group({\n      databaseField: ['', [Validators.required]]\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  ngAfterViewInit() {\n    console.log('AfterViewInit - Modal template reference:', this.variableMappingModal);\n  }\n  onTemplateBodyClick(event, languageIndex) {\n    console.log('Template body clicked!');\n    console.log('Channel type:', this.fValue?.channelType);\n    // Only handle clicks for SMS channel type\n    if (this.fValue?.channelType !== 'sms') {\n      console.log('Not SMS channel, ignoring click');\n      return;\n    }\n    const target = event.target;\n    const text = target.value || '';\n    const cursorPosition = target.selectionStart || 0;\n    console.log('Text:', text);\n    console.log('Cursor position:', cursorPosition);\n    // Find if cursor is within a [Var] placeholder\n    const varPattern = /\\[Var\\]/gi;\n    let match;\n    while ((match = varPattern.exec(text)) !== null) {\n      const start = match.index;\n      const end = match.index + match[0].length;\n      console.log(`Found [Var] at position ${start}-${end}, cursor at ${cursorPosition}`);\n      if (cursorPosition >= start && cursorPosition <= end) {\n        console.log('Cursor is within [Var], opening modal');\n        this.openVariableMappingModal(match[0], start, end, languageIndex);\n        break;\n      }\n    }\n  }\n  openVariableMappingModal(variable, startPos, endPos, languageIndex) {\n    console.log('openVariableMappingModal called with:', variable);\n    console.log('Modal template reference:', this.variableMappingModal);\n    console.log('Modal service:', this.modalService);\n    // First, let's test with a simple alert\n    alert(`Variable mapping requested for: ${variable} at position ${startPos}-${endPos}`);\n    // Store current mapping context\n    this.currentVariable = variable;\n    this.currentStartPos = startPos;\n    this.currentEndPos = endPos;\n    this.currentLanguageIndex = languageIndex;\n    // Reset form\n    this.variableMappingForm.reset();\n    // Check if template reference exists\n    if (!this.variableMappingModal) {\n      console.error('Modal template reference not found!');\n      alert('Modal template not found. Please check the template reference.');\n      return;\n    }\n    try {\n      // Open modal\n      console.log('Attempting to open modal...');\n      this.modalRef = this.modalService.show(this.variableMappingModal, {\n        class: 'modal-dialog-centered',\n        ignoreBackdropClick: true\n      });\n      console.log('Modal opened successfully:', this.modalRef);\n    } catch (error) {\n      console.error('Error opening modal:', error);\n      alert('Error opening modal: ' + error);\n    }\n  }\n  updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const templateBodyControl = languageGroup.get('templateBody');\n    if (templateBodyControl) {\n      const currentText = templateBodyControl.value || '';\n      const newText = currentText.substring(0, startPos) + `[${mappedField}]` + currentText.substring(endPos);\n      templateBodyControl.setValue(newText);\n    }\n  }\n  getVariableCount(templateBody) {\n    if (!templateBody) return 0;\n    const varPattern = /\\[Var\\]/gi;\n    const matches = templateBody.match(varPattern);\n    return matches ? matches.length : 0;\n  }\n  getMappedVariableCount(templateBody) {\n    if (!templateBody) return 0;\n    // Count variables that are not [Var] (i.e., already mapped)\n    const allVarPattern = /\\[[^\\]]+\\]/g;\n    const unmappedVarPattern = /\\[Var\\]/gi;\n    const allMatches = templateBody.match(allVarPattern);\n    const unmappedMatches = templateBody.match(unmappedVarPattern);\n    const totalVars = allMatches ? allMatches.length : 0;\n    const unmappedVars = unmappedMatches ? unmappedMatches.length : 0;\n    return totalVars - unmappedVars;\n  }\n  onHeaderUpload(event) {\n    // Handle header upload for letter type\n  }\n  onFooterUpload(event) {\n    // Handle footer upload for letter type\n  }\n  // Template-based modal methods\n  useSuggestedField(field) {\n    this.variableMappingForm.patchValue({\n      databaseField: field\n    });\n  }\n  mapVariableFromTemplate() {\n    if (this.variableMappingForm.valid) {\n      const mappedField = this.variableMappingForm.get('databaseField')?.value;\n      this.updateTemplateBodyWithMapping(mappedField, this.currentStartPos, this.currentEndPos, this.currentLanguageIndex);\n      this.closeVariableModal();\n    }\n  }\n  closeVariableModal() {\n    this.modalRef?.hide();\n    this.variableMappingForm.reset();\n  }\n  // Alternative method using a simple prompt for testing\n  openSimpleVariableMapping(variable, startPos, endPos, languageIndex) {\n    const mappedField = prompt(`Map variable \"${variable}\" to database field:`, 'user.first_name');\n    if (mappedField) {\n      this.updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex);\n    }\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      variableMappingModal: [{\n        type: ViewChild,\n        args: ['variableMappingModal']\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, SharedModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormArray", "FormBuilder", "FormsModule", "Validators", "SharedModule", "BsModalService", "CreateTemplateComponent", "constructor", "fb", "modalService", "currentVariable", "currentStartPos", "currentEndPos", "currentLanguageIndex", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbData", "label", "allLanguages", "name", "code", "buildCreateTemplateForm", "buildVariableMappingForm", "createForm", "group", "channelType", "templateName", "required", "activeLanguage", "languages", "buildLanguagesFormArray", "data", "formArray", "for<PERSON>ach", "o", "push", "buildLanguageFormGroup", "languageCode", "languageName", "emailSubject", "templateBody", "variableMappingForm", "databaseField", "fValue", "value", "ngAfterViewInit", "console", "log", "variableMappingModal", "onTemplateBodyClick", "event", "languageIndex", "target", "text", "cursorPosition", "selectionStart", "varPattern", "match", "exec", "start", "index", "end", "length", "openVariableMappingModal", "variable", "startPos", "endPos", "alert", "reset", "error", "modalRef", "show", "class", "ignoreBackdropClick", "updateTemplateBodyWithMapping", "mappedField", "languagesArray", "get", "languageGroup", "at", "templateBodyControl", "currentText", "newText", "substring", "setValue", "getVariableCount", "matches", "getMappedVariableCount", "allVarPattern", "unmappedVarPattern", "allMatches", "unmappedMatches", "totalVars", "unmappedVars", "onHeaderUpload", "onFooterUpload", "useSuggestedField", "field", "patchValue", "mapVariableFromTemplate", "valid", "closeVariableModal", "hide", "openSimpleVariableMapping", "prompt", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, ViewChild, TemplateRef, AfterViewInit } from \"@angular/core\";\r\nimport {\r\n  FormArray,\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from \"@angular/forms\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { BsModalService, BsModalRef } from \"ngx-bootstrap/modal\";\r\n\r\n@Component({\r\n  selector: \"app-create-template\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule],\r\n  templateUrl: \"./create-template.component.html\",\r\n  styleUrl: \"./create-template.component.scss\",\r\n})\r\nexport class CreateTemplateComponent implements AfterViewInit {\r\n  private fb: FormBuilder = inject(FormBuilder);\r\n  private modalService: BsModalService = inject(BsModalService);\r\n  modalRef?: BsModalRef;\r\n\r\n  @ViewChild('variableMappingModal') variableMappingModal!: TemplateRef<any>;\r\n\r\n  // Variable mapping form and data\r\n  variableMappingForm!: FormGroup;\r\n  currentVariable: string = '';\r\n  currentStartPos: number = 0;\r\n  currentEndPos: number = 0;\r\n  currentLanguageIndex: number = 0;\r\n\r\n  // Sample database field suggestions\r\n  suggestedFields = [\r\n    'user.first_name',\r\n    'user.last_name',\r\n    'user.email',\r\n    'user.phone',\r\n    'account.number',\r\n    'account.balance',\r\n    'payment.amount',\r\n    'payment.due_date',\r\n    'company.name'\r\n  ];\r\n\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Create Communication Template\" },\r\n  ];\r\n  allLanguages: any[] = [\r\n    { name: \"English\", code: \"en\" },\r\n    { name: \"Hindi\", code: \"hi\" },\r\n    { name: \"Tamil\", code: \"ta\" },\r\n    { name: \"Bengali\", code: \"bn\" },\r\n    { name: \"Telugu\", code: \"te\" },\r\n    { name: \"Marathi\", code: \"mr\" },\r\n    { name: \"Urdu\", code: \"ur\" },\r\n    { name: \"Gujarati\", code: \"gu\" },\r\n    { name: \"Kannada\", code: \"kn\" },\r\n    { name: \"Malayalam\", code: \"ml\" },\r\n    { name: \"Odia\", code: \"or\" },\r\n    { name: \"Punjabi\", code: \"pa\" },\r\n    { name: \"Assamese\", code: \"as\" },\r\n    { name: \"Maithili\", code: \"mai\" },\r\n    { name: \"Santali\", code: \"sat\" },\r\n    { name: \"Kashmiri\", code: \"ks\" },\r\n    { name: \"Konkani\", code: \"kok\" },\r\n    { name: \"Sindhi\", code: \"sd\" },\r\n    { name: \"Sanskrit\", code: \"sa\" },\r\n    { name: \"Manipuri\", code: \"mni\" },\r\n    { name: \"Bodo\", code: \"brx\" },\r\n    { name: \"Dogri\", code: \"doi\" },\r\n  ];\r\n  createForm!: FormGroup;\r\n\r\n  constructor() {\r\n    this.buildCreateTemplateForm();\r\n    this.buildVariableMappingForm();\r\n  }\r\n\r\n  buildCreateTemplateForm() {\r\n    this.createForm = this.fb.group({\r\n      channelType: [\"email\"],\r\n      templateName: [null, [Validators.required]],\r\n      activeLanguage: [this.allLanguages[0].code],\r\n      languages: this.buildLanguagesFormArray(),\r\n    });\r\n  }\r\n\r\n  buildLanguagesFormArray(data?: any[]) {\r\n    const formArray = new FormArray([]);\r\n    data = data || [this.allLanguages[0]];\r\n    data?.forEach((o) => {\r\n      formArray.push(this.buildLanguageFormGroup(o));\r\n    });\r\n    return formArray;\r\n  }\r\n\r\n  buildLanguageFormGroup(data?: any) {\r\n    return this.fb.group({\r\n      languageCode: data?.code,\r\n      languageName: data?.name,\r\n      emailSubject: [null],\r\n      templateBody: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  buildVariableMappingForm() {\r\n    this.variableMappingForm = this.fb.group({\r\n      databaseField: ['', [Validators.required]]\r\n    });\r\n  }\r\n\r\n  get fValue(): any {\r\n    return this.createForm.value;\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('AfterViewInit - Modal template reference:', this.variableMappingModal);\r\n  }\r\n\r\n  onTemplateBodyClick(event: MouseEvent, languageIndex: number) {\r\n    console.log('Template body clicked!');\r\n    console.log('Channel type:', this.fValue?.channelType);\r\n\r\n    // Only handle clicks for SMS channel type\r\n    if (this.fValue?.channelType !== 'sms') {\r\n      console.log('Not SMS channel, ignoring click');\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLTextAreaElement;\r\n    const text = target.value || '';\r\n    const cursorPosition = target.selectionStart || 0;\r\n\r\n    console.log('Text:', text);\r\n    console.log('Cursor position:', cursorPosition);\r\n\r\n    // Find if cursor is within a [Var] placeholder\r\n    const varPattern = /\\[Var\\]/gi;\r\n    let match;\r\n\r\n    while ((match = varPattern.exec(text)) !== null) {\r\n      const start = match.index;\r\n      const end = match.index + match[0].length;\r\n\r\n      console.log(`Found [Var] at position ${start}-${end}, cursor at ${cursorPosition}`);\r\n\r\n      if (cursorPosition >= start && cursorPosition <= end) {\r\n        console.log('Cursor is within [Var], opening modal');\r\n        this.openVariableMappingModal(match[0], start, end, languageIndex);\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  openVariableMappingModal(variable: string, startPos: number, endPos: number, languageIndex: number) {\r\n    console.log('openVariableMappingModal called with:', variable);\r\n    console.log('Modal template reference:', this.variableMappingModal);\r\n    console.log('Modal service:', this.modalService);\r\n\r\n    // First, let's test with a simple alert\r\n    alert(`Variable mapping requested for: ${variable} at position ${startPos}-${endPos}`);\r\n\r\n    // Store current mapping context\r\n    this.currentVariable = variable;\r\n    this.currentStartPos = startPos;\r\n    this.currentEndPos = endPos;\r\n    this.currentLanguageIndex = languageIndex;\r\n\r\n    // Reset form\r\n    this.variableMappingForm.reset();\r\n\r\n    // Check if template reference exists\r\n    if (!this.variableMappingModal) {\r\n      console.error('Modal template reference not found!');\r\n      alert('Modal template not found. Please check the template reference.');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Open modal\r\n      console.log('Attempting to open modal...');\r\n      this.modalRef = this.modalService.show(this.variableMappingModal, {\r\n        class: 'modal-dialog-centered',\r\n        ignoreBackdropClick: true\r\n      });\r\n      console.log('Modal opened successfully:', this.modalRef);\r\n    } catch (error) {\r\n      console.error('Error opening modal:', error);\r\n      alert('Error opening modal: ' + error);\r\n    }\r\n  }\r\n\r\n  updateTemplateBodyWithMapping(mappedField: string, startPos: number, endPos: number, languageIndex: number) {\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n    const languageGroup = languagesArray.at(languageIndex);\r\n    const templateBodyControl = languageGroup.get('templateBody');\r\n\r\n    if (templateBodyControl) {\r\n      const currentText = templateBodyControl.value || '';\r\n      const newText = currentText.substring(0, startPos) +\r\n                     `[${mappedField}]` +\r\n                     currentText.substring(endPos);\r\n      templateBodyControl.setValue(newText);\r\n    }\r\n  }\r\n\r\n  getVariableCount(templateBody: string): number {\r\n    if (!templateBody) return 0;\r\n    const varPattern = /\\[Var\\]/gi;\r\n    const matches = templateBody.match(varPattern);\r\n    return matches ? matches.length : 0;\r\n  }\r\n\r\n  getMappedVariableCount(templateBody: string): number {\r\n    if (!templateBody) return 0;\r\n    // Count variables that are not [Var] (i.e., already mapped)\r\n    const allVarPattern = /\\[[^\\]]+\\]/g;\r\n    const unmappedVarPattern = /\\[Var\\]/gi;\r\n\r\n    const allMatches = templateBody.match(allVarPattern);\r\n    const unmappedMatches = templateBody.match(unmappedVarPattern);\r\n\r\n    const totalVars = allMatches ? allMatches.length : 0;\r\n    const unmappedVars = unmappedMatches ? unmappedMatches.length : 0;\r\n\r\n    return totalVars - unmappedVars;\r\n  }\r\n\r\n\r\n\r\n  onHeaderUpload(event: any) {\r\n    // Handle header upload for letter type\r\n  }\r\n\r\n  onFooterUpload(event: any) {\r\n    // Handle footer upload for letter type\r\n  }\r\n\r\n  // Template-based modal methods\r\n  useSuggestedField(field: string) {\r\n    this.variableMappingForm.patchValue({\r\n      databaseField: field\r\n    });\r\n  }\r\n\r\n  mapVariableFromTemplate() {\r\n    if (this.variableMappingForm.valid) {\r\n      const mappedField = this.variableMappingForm.get('databaseField')?.value;\r\n      this.updateTemplateBodyWithMapping(mappedField, this.currentStartPos, this.currentEndPos, this.currentLanguageIndex);\r\n      this.closeVariableModal();\r\n    }\r\n  }\r\n\r\n  closeVariableModal() {\r\n    this.modalRef?.hide();\r\n    this.variableMappingForm.reset();\r\n  }\r\n\r\n  // Alternative method using a simple prompt for testing\r\n  openSimpleVariableMapping(variable: string, startPos: number, endPos: number, languageIndex: number) {\r\n    const mappedField = prompt(`Map variable \"${variable}\" to database field:`, 'user.first_name');\r\n    if (mappedField) {\r\n      this.updateTemplateBodyWithMapping(mappedField, startPos, endPos, languageIndex);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,SAAS,QAAoC,eAAe;AACxF,SACEC,SAAS,EACTC,WAAW,EAEXC,WAAW,EACXC,UAAU,QACL,gBAAgB;AACvB,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,cAAc,QAAoB,qBAAqB;AASzD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAyDlCC,YAAA;IAxDQ,KAAAC,EAAE,GAAgBV,MAAM,CAACG,WAAW,CAAC;IACrC,KAAAQ,YAAY,GAAmBX,MAAM,CAACO,cAAc,CAAC;IAO7D,KAAAK,eAAe,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAW,CAAC;IAC3B,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,oBAAoB,GAAW,CAAC;IAEhC;IACA,KAAAC,eAAe,GAAG,CAChB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,CACf;IAED,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IACD,KAAAC,YAAY,GAAU,CACpB;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,CAC/B;IAIC,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAD,uBAAuBA,CAAA;IACrB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,OAAO,CAAC;MACtBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACtB,UAAU,CAACuB,QAAQ,CAAC,CAAC;MAC3CC,cAAc,EAAE,CAAC,IAAI,CAACV,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC;MAC3CS,SAAS,EAAE,IAAI,CAACC,uBAAuB;KACxC,CAAC;EACJ;EAEAA,uBAAuBA,CAACC,IAAY;IAClC,MAAMC,SAAS,GAAG,IAAI/B,SAAS,CAAC,EAAE,CAAC;IACnC8B,IAAI,GAAGA,IAAI,IAAI,CAAC,IAAI,CAACb,YAAY,CAAC,CAAC,CAAC,CAAC;IACrCa,IAAI,EAAEE,OAAO,CAAEC,CAAC,IAAI;MAClBF,SAAS,CAACG,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,OAAOF,SAAS;EAClB;EAEAI,sBAAsBA,CAACL,IAAU;IAC/B,OAAO,IAAI,CAACtB,EAAE,CAACe,KAAK,CAAC;MACnBa,YAAY,EAAEN,IAAI,EAAEX,IAAI;MACxBkB,YAAY,EAAEP,IAAI,EAAEZ,IAAI;MACxBoB,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACpC,UAAU,CAACuB,QAAQ,CAAC;KAC3C,CAAC;EACJ;EAEAL,wBAAwBA,CAAA;IACtB,IAAI,CAACmB,mBAAmB,GAAG,IAAI,CAAChC,EAAE,CAACe,KAAK,CAAC;MACvCkB,aAAa,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACuB,QAAQ,CAAC;KAC1C,CAAC;EACJ;EAEA,IAAIgB,MAAMA,CAAA;IACR,OAAO,IAAI,CAACpB,UAAU,CAACqB,KAAK;EAC9B;EAEAC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACC,oBAAoB,CAAC;EACrF;EAEAC,mBAAmBA,CAACC,KAAiB,EAAEC,aAAqB;IAC1DL,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,MAAM,EAAElB,WAAW,CAAC;IAEtD;IACA,IAAI,IAAI,CAACkB,MAAM,EAAElB,WAAW,KAAK,KAAK,EAAE;MACtCqB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C;IACF;IAEA,MAAMK,MAAM,GAAGF,KAAK,CAACE,MAA6B;IAClD,MAAMC,IAAI,GAAGD,MAAM,CAACR,KAAK,IAAI,EAAE;IAC/B,MAAMU,cAAc,GAAGF,MAAM,CAACG,cAAc,IAAI,CAAC;IAEjDT,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEM,IAAI,CAAC;IAC1BP,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,cAAc,CAAC;IAE/C;IACA,MAAME,UAAU,GAAG,WAAW;IAC9B,IAAIC,KAAK;IAET,OAAO,CAACA,KAAK,GAAGD,UAAU,CAACE,IAAI,CAACL,IAAI,CAAC,MAAM,IAAI,EAAE;MAC/C,MAAMM,KAAK,GAAGF,KAAK,CAACG,KAAK;MACzB,MAAMC,GAAG,GAAGJ,KAAK,CAACG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACK,MAAM;MAEzChB,OAAO,CAACC,GAAG,CAAC,2BAA2BY,KAAK,IAAIE,GAAG,eAAeP,cAAc,EAAE,CAAC;MAEnF,IAAIA,cAAc,IAAIK,KAAK,IAAIL,cAAc,IAAIO,GAAG,EAAE;QACpDf,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAACgB,wBAAwB,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEE,KAAK,EAAEE,GAAG,EAAEV,aAAa,CAAC;QAClE;MACF;IACF;EACF;EAEAY,wBAAwBA,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,MAAc,EAAEf,aAAqB;IAChGL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEiB,QAAQ,CAAC;IAC9DlB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACC,oBAAoB,CAAC;IACnEF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACrC,YAAY,CAAC;IAEhD;IACAyD,KAAK,CAAC,mCAAmCH,QAAQ,gBAAgBC,QAAQ,IAAIC,MAAM,EAAE,CAAC;IAEtF;IACA,IAAI,CAACvD,eAAe,GAAGqD,QAAQ;IAC/B,IAAI,CAACpD,eAAe,GAAGqD,QAAQ;IAC/B,IAAI,CAACpD,aAAa,GAAGqD,MAAM;IAC3B,IAAI,CAACpD,oBAAoB,GAAGqC,aAAa;IAEzC;IACA,IAAI,CAACV,mBAAmB,CAAC2B,KAAK,EAAE;IAEhC;IACA,IAAI,CAAC,IAAI,CAACpB,oBAAoB,EAAE;MAC9BF,OAAO,CAACuB,KAAK,CAAC,qCAAqC,CAAC;MACpDF,KAAK,CAAC,gEAAgE,CAAC;MACvE;IACF;IAEA,IAAI;MACF;MACArB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAAC,IAAI,CAACvB,oBAAoB,EAAE;QAChEwB,KAAK,EAAE,uBAAuB;QAC9BC,mBAAmB,EAAE;OACtB,CAAC;MACF3B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACuB,QAAQ,CAAC;IAC1D,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CF,KAAK,CAAC,uBAAuB,GAAGE,KAAK,CAAC;IACxC;EACF;EAEAK,6BAA6BA,CAACC,WAAmB,EAAEV,QAAgB,EAAEC,MAAc,EAAEf,aAAqB;IACxG,MAAMyB,cAAc,GAAG,IAAI,CAACrD,UAAU,CAACsD,GAAG,CAAC,WAAW,CAAc;IACpE,MAAMC,aAAa,GAAGF,cAAc,CAACG,EAAE,CAAC5B,aAAa,CAAC;IACtD,MAAM6B,mBAAmB,GAAGF,aAAa,CAACD,GAAG,CAAC,cAAc,CAAC;IAE7D,IAAIG,mBAAmB,EAAE;MACvB,MAAMC,WAAW,GAAGD,mBAAmB,CAACpC,KAAK,IAAI,EAAE;MACnD,MAAMsC,OAAO,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAElB,QAAQ,CAAC,GACnC,IAAIU,WAAW,GAAG,GAClBM,WAAW,CAACE,SAAS,CAACjB,MAAM,CAAC;MAC5Cc,mBAAmB,CAACI,QAAQ,CAACF,OAAO,CAAC;IACvC;EACF;EAEAG,gBAAgBA,CAAC7C,YAAoB;IACnC,IAAI,CAACA,YAAY,EAAE,OAAO,CAAC;IAC3B,MAAMgB,UAAU,GAAG,WAAW;IAC9B,MAAM8B,OAAO,GAAG9C,YAAY,CAACiB,KAAK,CAACD,UAAU,CAAC;IAC9C,OAAO8B,OAAO,GAAGA,OAAO,CAACxB,MAAM,GAAG,CAAC;EACrC;EAEAyB,sBAAsBA,CAAC/C,YAAoB;IACzC,IAAI,CAACA,YAAY,EAAE,OAAO,CAAC;IAC3B;IACA,MAAMgD,aAAa,GAAG,aAAa;IACnC,MAAMC,kBAAkB,GAAG,WAAW;IAEtC,MAAMC,UAAU,GAAGlD,YAAY,CAACiB,KAAK,CAAC+B,aAAa,CAAC;IACpD,MAAMG,eAAe,GAAGnD,YAAY,CAACiB,KAAK,CAACgC,kBAAkB,CAAC;IAE9D,MAAMG,SAAS,GAAGF,UAAU,GAAGA,UAAU,CAAC5B,MAAM,GAAG,CAAC;IACpD,MAAM+B,YAAY,GAAGF,eAAe,GAAGA,eAAe,CAAC7B,MAAM,GAAG,CAAC;IAEjE,OAAO8B,SAAS,GAAGC,YAAY;EACjC;EAIAC,cAAcA,CAAC5C,KAAU;IACvB;EAAA;EAGF6C,cAAcA,CAAC7C,KAAU;IACvB;EAAA;EAGF;EACA8C,iBAAiBA,CAACC,KAAa;IAC7B,IAAI,CAACxD,mBAAmB,CAACyD,UAAU,CAAC;MAClCxD,aAAa,EAAEuD;KAChB,CAAC;EACJ;EAEAE,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAAC1D,mBAAmB,CAAC2D,KAAK,EAAE;MAClC,MAAMzB,WAAW,GAAG,IAAI,CAAClC,mBAAmB,CAACoC,GAAG,CAAC,eAAe,CAAC,EAAEjC,KAAK;MACxE,IAAI,CAAC8B,6BAA6B,CAACC,WAAW,EAAE,IAAI,CAAC/D,eAAe,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAACC,oBAAoB,CAAC;MACpH,IAAI,CAACuF,kBAAkB,EAAE;IAC3B;EACF;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAAC/B,QAAQ,EAAEgC,IAAI,EAAE;IACrB,IAAI,CAAC7D,mBAAmB,CAAC2B,KAAK,EAAE;EAClC;EAEA;EACAmC,yBAAyBA,CAACvC,QAAgB,EAAEC,QAAgB,EAAEC,MAAc,EAAEf,aAAqB;IACjG,MAAMwB,WAAW,GAAG6B,MAAM,CAAC,iBAAiBxC,QAAQ,sBAAsB,EAAE,iBAAiB,CAAC;IAC9F,IAAIW,WAAW,EAAE;MACf,IAAI,CAACD,6BAA6B,CAACC,WAAW,EAAEV,QAAQ,EAAEC,MAAM,EAAEf,aAAa,CAAC;IAClF;EACF;;;;;;;cAnPCnD,SAAS;QAAAyG,IAAA,GAAC,sBAAsB;MAAA;;;;AALtBlG,uBAAuB,GAAAmG,UAAA,EAPnC5G,SAAS,CAAC;EACT6G,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1G,WAAW,EAAEE,YAAY,CAAC;EACpCyG,QAAA,EAAAC,oBAA+C;;CAEhD,CAAC,C,EACWxG,uBAAuB,CAyPnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}