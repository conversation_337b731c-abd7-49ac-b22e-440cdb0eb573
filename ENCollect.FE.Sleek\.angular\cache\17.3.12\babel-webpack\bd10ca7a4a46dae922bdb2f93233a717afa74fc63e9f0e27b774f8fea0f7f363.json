{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from \"@angular/core\";\nimport { RouterModule } from \"@angular/router\";\nimport { Error404Component } from \"./public/error404/error404.component\";\nimport { Error500Component } from \"./public/error500/error500/error500.component\";\nimport { AuthGuard } from \"./shared\";\nimport { UiElementsComponent } from \"./ui-elements/ui-elements.component\";\nimport { CodeOfConductComponent } from \"./public/code-of-conduct/code-of-conduct.component\";\nimport { communicationRoutes } from \"./communication/communication.routes\";\nconst appRoutes = [{\n  path: \"\",\n  loadChildren: () => import(\"./auth/auth.module\").then(m => m.AuthModule)\n}, {\n  path: \"action\",\n  loadChildren: () => import(\"./action/action.module\").then(m => m.ActionModule)\n}, {\n  path: \"encollect/v1\",\n  loadChildren: () => import(\"./dashboard/dashboard.module\").then(m => m.DashboardModule)\n}, {\n  path: \"encollect/allocation/v1\",\n  loadChildren: () => import(\"./allocation/allocation.module\").then(m => m.AllocationModule)\n}, {\n  path: \"encollect/payments/v1\",\n  loadChildren: () => import(\"./payments/payment.module\").then(m => m.PaymentModule)\n}, {\n  path: \"encollect/agent/v1\",\n  loadChildren: () => import(\"./user-management/agent/agent.module\").then(m => m.AgentModule)\n}, {\n  path: \"encollect/agency/v1\",\n  loadChildren: () => import(\"./user-management/agencyEmpanelment/agencyEmpanelment.module\").then(m => m.AgencyEmpanelmentModule)\n}, {\n  path: \"encollect/staff/v1\",\n  loadChildren: () => import(\"./user-management/staff/staff.module\").then(m => m.CollectionStaffModule)\n}, {\n  path: \"encollect/users/v1\",\n  loadChildren: () => import(\"./user-management/userManagement.module\").then(m => m.UserManagementModule)\n}, {\n  path: \"settlement\",\n  loadChildren: () => import(\"./settlement/settlement.module\").then(m => m.SettlementModule)\n}, {\n  path: \"segmentation\",\n  loadChildren: () => import(\"./segmentation/segmentation.module\").then(m => m.SegmentationModule)\n}, {\n  path: \"search\",\n  loadChildren: () => import(\"./search/search.module\").then(m => m.SearchModule)\n}, {\n  path: \"treatment\",\n  loadChildren: () => import(\"./Treatment/treatment.module\").then(m => m.TreatmentModule)\n}, {\n  path: \"value\",\n  loadChildren: () => import(\"./value/value.module\").then(m => m.ValueModule)\n}, {\n  path: \"workflows\",\n  loadChildren: () => import(\"./workflow/workflow.module\").then(m => m.WorkflowModule)\n}, {\n  path: \"reports\",\n  loadChildren: () => import(\"./reports/reports.module\").then(m => m.ReportsModule)\n}, {\n  path: \"insights\",\n  loadChildren: () => import(\"./insights/insights.module\").then(m => m.InsightsModule)\n}, {\n  path: \"demo-reports\",\n  loadChildren: () => import(\"./demo-reports/demoReports.module\").then(m => m.DemoReportsModule)\n}, {\n  path: \"settings\",\n  loadChildren: () => import(\"./settings/settings.module\").then(m => m.SettingsModule)\n}, {\n  path: \"communication\",\n  children: communicationRoutes\n}, {\n  path: \"communication\",\n  children: communicationRoutes\n}, {\n  path: \"attendance\",\n  loadChildren: () => import(\"./attendance/attendance.module\").then(m => m.AttendanceModule)\n}, {\n  path: \"travel-report\",\n  loadChildren: () => import(\"./travel-report/travel-report.module\").then(m => m.TravelReportModule)\n}, {\n  path: \"encollect/dashboard\",\n  loadChildren: () => import(\"./dashboard/dashboard.module\").then(m => m.DashboardModule)\n}, {\n  path: \"encollect/cure\",\n  loadChildren: () => import(\"./click-to-cure/clicktocure.module\").then(m => m.ClicktoCureModule)\n}, {\n  path: \"encollect/legal\",\n  loadChildren: () => import(\"./legal/legal.module\").then(m => m.LegalModule)\n}, {\n  path: \"encollect/legal-custom\",\n  loadChildren: () => import(\"./legal-custom/legal-custom.module\").then(m => m.LegalCustomModule)\n}, {\n  path: \"encollect/repossession\",\n  loadChildren: () => import(\"./repo/repo.module\").then(m => m.RepoModule)\n}, {\n  path: \"encollect/uploads/v1\",\n  loadChildren: () => import(\"./upload/upload.module\").then(m => m.UploadModule)\n},\n// TODO:: Not in Use, Need to Remove\n{\n  path: \"digital\",\n  loadChildren: () => import(\"./digital-id/digital-id.module\").then(m => m.DigitalIdModule)\n}, {\n  path: \"target\",\n  loadChildren: () => import(\"./target/target.module\").then(m => m.TargetModule)\n}, {\n  path: \"ui-elements\",\n  component: UiElementsComponent\n}, {\n  path: \"code-of-conduct\",\n  component: CodeOfConductComponent\n}, {\n  path: \"**\",\n  component: Error404Component\n}, {\n  path: \"error-500\",\n  component: Error500Component,\n  canActivate: [AuthGuard]\n}\n//import{TravelReportModule} from './travel-report/travel-report.module'\n// {\n//     path: '**',\n//     // resolve: {\n//     //   path: PathResolveService\n//     // },\n//     component: NotFoundComponent\n// }\n];\nlet AppRoutingModule = class AppRoutingModule {};\nAppRoutingModule = __decorate([NgModule({\n  imports: [RouterModule.forRoot(appRoutes, {\n    useHash: true,\n    initialNavigation: \"enabledNonBlocking\"\n  })],\n  exports: [RouterModule]\n})], AppRoutingModule);\nexport { AppRoutingModule };", "map": {"version": 3, "names": ["NgModule", "RouterModule", "Error404Component", "Error500Component", "<PERSON><PERSON><PERSON><PERSON>", "UiElementsComponent", "CodeOfConductComponent", "communicationRoutes", "appRoutes", "path", "loadChildren", "then", "m", "AuthModule", "ActionModule", "DashboardModule", "AllocationModule", "PaymentModule", "AgentModule", "AgencyEmpanelmentModule", "CollectionStaffModule", "UserManagementModule", "SettlementModule", "SegmentationModule", "SearchModule", "TreatmentModule", "ValueModule", "WorkflowModule", "ReportsModule", "InsightsModule", "DemoReportsModule", "SettingsModule", "children", "AttendanceModule", "TravelReportModule", "ClicktoCureModule", "LegalModule", "LegalCustomModule", "RepoModule", "UploadModule", "DigitalIdModule", "TargetModule", "component", "canActivate", "AppRoutingModule", "__decorate", "imports", "forRoot", "useHash", "initialNavigation", "exports"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\r\nimport { RouterModule, Routes } from \"@angular/router\";\r\nimport { Error404Component } from \"./public/error404/error404.component\";\r\nimport { Error500Component } from \"./public/error500/error500/error500.component\";\r\nimport { AuthGuard } from \"./shared\";\r\nimport { UiElementsComponent } from \"./ui-elements/ui-elements.component\";\r\nimport { CodeOfConductComponent } from \"./public/code-of-conduct/code-of-conduct.component\";\r\nimport { communicationRoutes } from \"./communication/communication.routes\";\r\nconst appRoutes: Routes = [\r\n  {\r\n    path: \"\",\r\n    loadChildren: () => import(\"./auth/auth.module\").then((m) => m.AuthModule),\r\n  },\r\n  {\r\n    path: \"action\",\r\n    loadChildren: () =>\r\n      import(\"./action/action.module\").then((m) => m.ActionModule),\r\n  },\r\n  {\r\n    path: \"encollect/v1\",\r\n    loadChildren: () =>\r\n      import(\"./dashboard/dashboard.module\").then((m) => m.DashboardModule),\r\n  },\r\n  {\r\n    path: \"encollect/allocation/v1\",\r\n    loadChildren: () =>\r\n      import(\"./allocation/allocation.module\").then((m) => m.AllocationModule),\r\n  },\r\n  {\r\n    path: \"encollect/payments/v1\",\r\n    loadChildren: () =>\r\n      import(\"./payments/payment.module\").then((m) => m.PaymentModule),\r\n  },\r\n  {\r\n    path: \"encollect/agent/v1\",\r\n    loadChildren: () =>\r\n      import(\"./user-management/agent/agent.module\").then((m) => m.AgentModule),\r\n  },\r\n  {\r\n    path: \"encollect/agency/v1\",\r\n    loadChildren: () =>\r\n      import(\r\n        \"./user-management/agencyEmpanelment/agencyEmpanelment.module\"\r\n      ).then((m) => m.AgencyEmpanelmentModule),\r\n  },\r\n  {\r\n    path: \"encollect/staff/v1\",\r\n    loadChildren: () =>\r\n      import(\"./user-management/staff/staff.module\").then(\r\n        (m) => m.CollectionStaffModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"encollect/users/v1\",\r\n    loadChildren: () =>\r\n      import(\"./user-management/userManagement.module\").then(\r\n        (m) => m.UserManagementModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"settlement\",\r\n    loadChildren: () =>\r\n      import(\"./settlement/settlement.module\").then((m) => m.SettlementModule),\r\n  },\r\n  {\r\n    path: \"segmentation\",\r\n    loadChildren: () =>\r\n      import(\"./segmentation/segmentation.module\").then(\r\n        (m) => m.SegmentationModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"search\",\r\n    loadChildren: () =>\r\n      import(\"./search/search.module\").then((m) => m.SearchModule),\r\n  },\r\n  {\r\n    path: \"treatment\",\r\n    loadChildren: () =>\r\n      import(\"./Treatment/treatment.module\").then((m) => m.TreatmentModule),\r\n  },\r\n  {\r\n    path: \"value\",\r\n    loadChildren: () =>\r\n      import(\"./value/value.module\").then((m) => m.ValueModule),\r\n  },\r\n  {\r\n    path: \"workflows\",\r\n    loadChildren: () =>\r\n      import(\"./workflow/workflow.module\").then((m) => m.WorkflowModule),\r\n  },\r\n  {\r\n    path: \"reports\",\r\n    loadChildren: () =>\r\n      import(\"./reports/reports.module\").then((m) => m.ReportsModule),\r\n  },\r\n  {\r\n    path: \"insights\",\r\n    loadChildren: () =>\r\n      import(\"./insights/insights.module\").then((m) => m.InsightsModule),\r\n  },\r\n  {\r\n    path: \"demo-reports\",\r\n    loadChildren: () =>\r\n      import(\"./demo-reports/demoReports.module\").then(\r\n        (m) => m.DemoReportsModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"settings\",\r\n    loadChildren: () =>\r\n      import(\"./settings/settings.module\").then((m) => m.SettingsModule),\r\n  },\r\n  {\r\n    path: \"communication\",\r\n    children: communicationRoutes,\r\n  },\r\n  {\r\n    path: \"communication\",\r\n    children: communicationRoutes,\r\n  },\r\n  {\r\n    path: \"attendance\",\r\n    loadChildren: () =>\r\n      import(\"./attendance/attendance.module\").then((m) => m.AttendanceModule),\r\n  },\r\n  {\r\n    path: \"travel-report\",\r\n    loadChildren: () =>\r\n      import(\"./travel-report/travel-report.module\").then(\r\n        (m) => m.TravelReportModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"encollect/dashboard\",\r\n    loadChildren: () =>\r\n      import(\"./dashboard/dashboard.module\").then((m) => m.DashboardModule),\r\n  },\r\n  {\r\n    path: \"encollect/cure\",\r\n    loadChildren: () =>\r\n      import(\"./click-to-cure/clicktocure.module\").then(\r\n        (m) => m.ClicktoCureModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"encollect/legal\",\r\n    loadChildren: () =>\r\n      import(\"./legal/legal.module\").then((m) => m.LegalModule),\r\n  },\r\n  {\r\n    path: \"encollect/legal-custom\",\r\n    loadChildren: () =>\r\n      import(\"./legal-custom/legal-custom.module\").then(\r\n        (m) => m.LegalCustomModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"encollect/repossession\",\r\n    loadChildren: () => import(\"./repo/repo.module\").then((m) => m.RepoModule),\r\n  },\r\n  {\r\n    path: \"encollect/uploads/v1\",\r\n    loadChildren: () =>\r\n      import(\"./upload/upload.module\").then((m) => m.UploadModule),\r\n  }, // TODO:: Not in Use, Need to Remove\r\n  {\r\n    path: \"digital\",\r\n    loadChildren: () =>\r\n      import(\"./digital-id/digital-id.module\").then((m) => m.DigitalIdModule),\r\n  },\r\n  {\r\n    path: \"target\",\r\n    loadChildren: () =>\r\n      import(\"./target/target.module\").then((m) => m.TargetModule),\r\n  },\r\n  {\r\n    path: \"ui-elements\",\r\n    component: UiElementsComponent,\r\n  },\r\n  {\r\n    path: \"code-of-conduct\",\r\n    component: CodeOfConductComponent,\r\n  },\r\n  { path: \"**\", component: Error404Component },\r\n  { path: \"error-500\", component: Error500Component, canActivate: [AuthGuard] },\r\n  //import{TravelReportModule} from './travel-report/travel-report.module'\r\n\r\n  // {\r\n  //     path: '**',\r\n  //     // resolve: {\r\n  //     //   path: PathResolveService\r\n  //     // },\r\n  //     component: NotFoundComponent\r\n  // }\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forRoot(appRoutes, {\r\n      useHash: true,\r\n      initialNavigation: \"enabledNonBlocking\",\r\n    }),\r\n  ],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,sBAAsB,QAAQ,oDAAoD;AAC3F,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,MAAMC,SAAS,GAAW,CACxB;EACEC,IAAI,EAAE,EAAE;EACRC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;CAC1E,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,YAAY;CAC9D,EACD;EACEL,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,eAAe;CACvE,EACD;EACEN,IAAI,EAAE,yBAAyB;EAC/BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,gBAAgB;CAC1E,EACD;EACEP,IAAI,EAAE,uBAAuB;EAC7BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,2BAA2B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,aAAa;CAClE,EACD;EACER,IAAI,EAAE,oBAAoB;EAC1BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,WAAW;CAC3E,EACD;EACET,IAAI,EAAE,qBAAqB;EAC3BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CACJ,8DAA8D,CAC/D,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,uBAAuB;CAC1C,EACD;EACEV,IAAI,EAAE,oBAAoB;EAC1BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACQ,qBAAqB;CAEnC,EACD;EACEX,IAAI,EAAE,oBAAoB;EAC1BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CACnDC,CAAC,IAAKA,CAAC,CAACS,oBAAoB;CAElC,EACD;EACEZ,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,gBAAgB;CAC1E,EACD;EACEb,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACW,kBAAkB;CAEhC,EACD;EACEd,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACY,YAAY;CAC9D,EACD;EACEf,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACa,eAAe;CACvE,EACD;EACEhB,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACc,WAAW;CAC3D,EACD;EACEjB,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACe,cAAc;CACpE,EACD;EACElB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACgB,aAAa;CACjE,EACD;EACEnB,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACiB,cAAc;CACpE,EACD;EACEpB,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAC7CC,CAAC,IAAKA,CAAC,CAACkB,iBAAiB;CAE/B,EACD;EACErB,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACmB,cAAc;CACpE,EACD;EACEtB,IAAI,EAAE,eAAe;EACrBuB,QAAQ,EAAEzB;CACX,EACD;EACEE,IAAI,EAAE,eAAe;EACrBuB,QAAQ,EAAEzB;CACX,EACD;EACEE,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACqB,gBAAgB;CAC1E,EACD;EACExB,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACsB,kBAAkB;CAEhC,EACD;EACEzB,IAAI,EAAE,qBAAqB;EAC3BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,eAAe;CACvE,EACD;EACEN,IAAI,EAAE,gBAAgB;EACtBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACuB,iBAAiB;CAE/B,EACD;EACE1B,IAAI,EAAE,iBAAiB;EACvBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACwB,WAAW;CAC3D,EACD;EACE3B,IAAI,EAAE,wBAAwB;EAC9BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACyB,iBAAiB;CAE/B,EACD;EACE5B,IAAI,EAAE,wBAAwB;EAC9BC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC0B,UAAU;CAC1E,EACD;EACE7B,IAAI,EAAE,sBAAsB;EAC5BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC2B,YAAY;CAC9D;AAAE;AACH;EACE9B,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC4B,eAAe;CACzE,EACD;EACE/B,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC6B,YAAY;CAC9D,EACD;EACEhC,IAAI,EAAE,aAAa;EACnBiC,SAAS,EAAErC;CACZ,EACD;EACEI,IAAI,EAAE,iBAAiB;EACvBiC,SAAS,EAAEpC;CACZ,EACD;EAAEG,IAAI,EAAE,IAAI;EAAEiC,SAAS,EAAExC;AAAiB,CAAE,EAC5C;EAAEO,IAAI,EAAE,WAAW;EAAEiC,SAAS,EAAEvC,iBAAiB;EAAEwC,WAAW,EAAE,CAACvC,SAAS;AAAC;AAC3E;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD;AAWM,IAAMwC,gBAAgB,GAAtB,MAAMA,gBAAgB,GAAG;AAAnBA,gBAAgB,GAAAC,UAAA,EAT5B7C,QAAQ,CAAC;EACR8C,OAAO,EAAE,CACP7C,YAAY,CAAC8C,OAAO,CAACvC,SAAS,EAAE;IAC9BwC,OAAO,EAAE,IAAI;IACbC,iBAAiB,EAAE;GACpB,CAAC,CACH;EACDC,OAAO,EAAE,CAACjD,YAAY;CACvB,CAAC,C,EACW2C,gBAAgB,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}