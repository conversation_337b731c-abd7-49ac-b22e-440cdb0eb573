{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./menu-layout.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./menu-layout.component.scss?ngResource\";\nimport { Component, inject } from \"@angular/core\";\nimport { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router } from \"@angular/router\";\nimport { interval, map } from \"rxjs\";\nimport { UserService } from \"src/app/authentication/user.service\";\nimport { ACMP, AcmService } from \"../../services\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { JWTTokenService } from \"../../services/jwt-token.service\";\nconst leftMenuMode = localStorage.getItem(\"leftMenuMode\") || \"push\";\nlet MenuLayoutComponent = class MenuLayoutComponent {\n  constructor(router, userService, acmService, toastr) {\n    this.router = router;\n    this.userService = userService;\n    this.acmService = acmService;\n    this.toastr = toastr;\n    this.jwtTokenService = inject(JWTTokenService);\n    this.currentTime$ = interval(1000).pipe(map(() => new Date()));\n    this.isLoading = false;\n    this.isSideNavExpanded = leftMenuMode === 'push';\n    this.leftMenuMode = leftMenuMode;\n    this.profileImage = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAADmCAAAAADkAaZnAAAFCElEQVR42u3cV7LbPAwFYO1/i6C61ahiq1j2zV+Sh8xkEsdVBGyA5FkBviHFIlIKvjuRwDM90zM984PM/52IZ3qmZ3qmZ3qmZ3qmZ9rM/M+JeKZneqZneqZneqZnOsr814l4pmd6pmd6pmd6pmd6ps3Mf5yIZ3qmZ3qmU8zZAeaxjeFn8mp/tJH5NbdFCH8kLPqTVcxTk8L1xNX4ZQnzmMPd5OSNGnwjz1rB48RNm/YrWQ30TK3g6WSTUOaUwEvJzhKZGl6NWsQxlwSAh5OSOSjYEnUWxaxhY3aCmOcSNmcSwzwn25WQrUKYRkqA6CyCaagEAKUFMI2VACUu84sgJSAkmREromBqwEnPmjkCVmrGzJNCY0LLl5kBYvZcmT2mEtSJJxOzywIA7HgyS0DOyJF5xFZCxpGJ3pgAMz8mfmMiTZ4B98YEhcI8I2YBikwIlaEyGxJmw40ZkTAzZsyJRAmKGbOgYcLKixkRMQ+smER9lhuzpWI2rJiZG0xwgjmxZp6w0tExzYvDYzZuMMlGIBjcYB7cYI5uME9OMCM3mBkrZsF4PpEwbw6smJqKecRgrljZEylzjOLwmAsRs+fFXGnekaiVGTMnYZbcmDRj0IEbk2RfnazcmGtCwNT8mBSv9hZ+zIXppInMXJlOmrjMpWA6aaIyDxHbcXZdgyNWUpKVHlJxaMyRZHEQcWMORLswJ5iKG3MmYabcmEeSfVjDjrkjeRPNjklxIhYf2TEpem3DkElwYW9gyKzxma0bzBqNuaCFYKitsGpDZKaeaRGToNM2DJkHgu0mQyZBcy4cmRPyF0WQsWQuFTJT82QiN6daeDIXzXM6wWYumLuUGLGuYEZNH2MZs2pCrAuZOc89yv2gDLkqdOY8Y3Tcij8T4yil5s/EmD17N5iDG8zZCWbsBjN1g1kJYCL8x0KjMyf0IBzoDtg1ETAn8+XeJIFpfE86FsE8mO6ucxHMyfTKaSODaTrW9jKYk9nmWk1CmGYrhEQK0+wrhlIKcwpNmJ0YZsLr0aRimjycGQVzJInJ6VhDUA8RczS4VjIIYu43L/iSURBz3HqcojpRzHHjYFuNspg7Pl2WkLnx3/WVLOZ+6+q9FMXcvuXMBTFNNta5GKbZ64NaBnNv+oWjxmce0NOFhkpQHXZN+EyMU1x0JzZT49wlUZozc8D7/8pu4MocCsxbXmHNkomLRIYGbJEAAGExcGL2ZP9EUkXPhalToAzGYGTOrCMgjjLvuoHpIxnCG6IKU+beIP1OwZsS1iaF7g2YXQbvTFi0H2DqBN6fKCkqrfU7mF1dJCF8OCrJypaOWecfF/5mTauegNnlCrglrZGZH3kYnxmaKkQmV+ST0OeYXQqsE2oMZqmAe9LelNklICBKmzG1AhnJ7zGHBylBTJL+puIRMwNBibqNTFFKAKU3MYUpbzsDq5QAqn2ZKVB56/m8wyxAZKLXmA0ITf4Ks1NSmVC/wIzFKq8NQ4FdD+bPxH8z+6tplWQmlJeeG8xUtBJU+xSzBuHJnmLG0pmgn2CKb0yA9Amm/Ma8bM5rzMYCJeweMlMbmOoRU/iceXXuvMIsrVBC8oCZ2MGE9i6ztUT5R68NusuUtjDT31B/MzNbmOouM7KFCfU9pjVKKO4wK3uY2R3mzh5mfIeZ28MM7zBje5jgCFPfZkYWMavbTLCT2V7EJmb5S3XJbGxi5jeZlWfK26PcZBY2MeNfrB9mJmk0jeOmPQAAAABJRU5ErkJggg==`; // assets/new/svgs/profile_img.svg\n    this.logoUrl = \"\";\n    this.permissions = [];\n    this.isAuthenticated$ = this.userService.isAuthenticated;\n    this.fetchCurrentUser();\n    this.isAuthenticated$.subscribe(isUpdated => {\n      if (isUpdated) {\n        this.buildNavList();\n      }\n      this.permissions = this.jwtTokenService.getPermissions();\n    });\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.isLoading = true;\n      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\n        this.isLoading = false;\n      }\n    });\n  }\n  fetchCurrentUser() {\n    this.userService.currentUser.subscribe(userData => {\n      this.currentUser = userData;\n      this.primaryRole = this.currentUser?.roles?.find(o => o?.isPrimaryDesignation) || this.currentUser?.roles?.[0];\n      this.loadProfileImage(userData?.profileImage);\n      this.getLogoImage(userData);\n    });\n  }\n  toggleSideNav() {\n    this.leftMenuMode = this.leftMenuMode === 'push' ? 'over' : 'push';\n    this.isSideNavExpanded = this.leftMenuMode === 'push';\n    localStorage.setItem(\"leftMenuMode\", `${this.leftMenuMode}`);\n  }\n  hoverSideNav(value) {\n    if (this.leftMenuMode === 'over') {\n      this.isSideNavExpanded = value;\n    }\n  }\n  onSelectMenuItem(item, items) {\n    if (item?.subMenus?.length) {\n      items.forEach(o => {\n        if (item.name !== o.name) o[\"expanded\"] = false;\n      });\n      item[\"expanded\"] = !item?.expanded;\n    } else if (item?.path) {\n      this.router.navigate([item?.path]);\n    }\n  }\n  buildNavList() {\n    const configNav = navList => navList.reduce((list, item) => {\n      if (item?.subMenus?.length) {\n        const children = configNav(item?.subMenus);\n        item.subMenus = children;\n        item.hasAccess = children.some(item => item.hasAccess);\n        item.class = item?.name?.toLowerCase()?.replace(/ & /g, \"-\")?.replace(/ /g, \"-\");\n      } else if (item?.acm?.length) {\n        item.hasAccess = this.acmService.hasACMAccess(item?.acm);\n      } else if (item?.roles?.length) {\n        item.hasAccess = this.acmService.hasRoleAccess(item?.roles);\n      } else {\n        item.hasAccess = item?.hasAccess ?? false;\n      }\n      delete item?.acm;\n      list.push(item);\n      return list;\n    }, []);\n    const navList = configNav(MENU_LIST());\n    this.navList = navList;\n  }\n  ngOnDestroy() {}\n  loadProfileImage(profileImage) {\n    if (profileImage) {\n      this.userService.filePreview(profileImage).subscribe(res => {\n        const mediaType = \"image/jpeg\";\n        const arrayBufferView = new Uint8Array(res);\n        const file = new Blob([arrayBufferView], {\n          type: mediaType\n        });\n        const myReader = new FileReader();\n        myReader.onloadend = e => {\n          this.profileImage = myReader.result;\n        };\n        myReader.readAsDataURL(file);\n      });\n    }\n  }\n  getLogoImage(user) {\n    if (!user?.id) return;\n    this.userService.getLogo().subscribe(res => {\n      const mediaType = \"image/jpeg\";\n      const arrayBufferView = new Uint8Array(res);\n      const file = new Blob([arrayBufferView], {\n        type: mediaType\n      });\n      const myReader = new FileReader();\n      myReader.onloadend = e => {\n        this.logoUrl = myReader.result;\n      };\n      myReader.readAsDataURL(file);\n    }, err => {\n      this.toastr.error(\"Logo is not found\");\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: UserService\n    }, {\n      type: AcmService\n    }, {\n      type: ToastrService\n    }];\n  }\n};\nMenuLayoutComponent = __decorate([Component({\n  selector: \"app-menu-layout\",\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], MenuLayoutComponent);\nexport { MenuLayoutComponent };\nconst MENU_LIST = () => [{\n  name: \"Account Search\",\n  icon: \"nav_dash_icon\",\n  path: \"/encollect/v1/dashboard\",\n  // hasAccess: true,\n  acm: [ACMP.CanSearchAccounts]\n}, {\n  name: \"User Management\",\n  icon: \"nav_user_mgmt_icon\",\n  subMenus: [{\n    name: \"Agency Empanelment\",\n    subMenus: [{\n      name: \"Add Agency\",\n      path: \"/encollect/agency/v1/agencyEmpanelment-create\",\n      acm: [ACMP.CanCreateAgency]\n    }, {\n      name: \"Search Agency\",\n      path: \"/encollect/agency/v1/agencyEmpanelment-search\",\n      acm: [ACMP.CanSearchAgency]\n    }]\n  }, {\n    name: \"Agent Empanelment\",\n    subMenus: [{\n      name: \"Add Agent\",\n      path: \"/encollect/agent/v1/create\",\n      acm: [ACMP.CanCreateAgent]\n    }, {\n      name: \"Search Agent\",\n      path: \"/encollect/agent/v1/search\",\n      acm: [ACMP.CanSearchAgent]\n    }]\n  }, {\n    name: \"Staff Empanelment\",\n    subMenus: [{\n      name: \"Add Staff\",\n      path: \"/encollect/staff/v1/create-collection-staff\",\n      acm: [ACMP.CanCreateStaff]\n    }, {\n      name: \"Search Staff\",\n      path: \"/encollect/staff/v1/search-collection-staff\",\n      acm: [ACMP.CanSearchStaff]\n    }]\n  }, {\n    name: \"Locked Profiles\",\n    path: \"/encollect/users/v1/locked-profiles\",\n    hide: true\n    // acm: [ACM.UMLockedProfilesScreen], // TODO: ACM Configuration\n  }, {\n    name: \"Bulk Upload\",\n    subMenus: [{\n      name: \"User Creation Upload\",\n      path: \"/encollect/users/v1/bulk-user-upload\",\n      acm: [ACMP.CanUploadBulkUser]\n    }, {\n      name: \"User Creation Upload Status\",\n      path: \"/encollect/users/v1/bulk-user-upload-status\",\n      acm: [ACMP.CanSearchBulkUserUploadStatus]\n    }, {\n      name: \"Bulk Enable/Disable Users\",\n      path: \"/encollect/users/v1/bulk-upload-enable-disable\",\n      acm: [ACMP.CanUploadBulkEnableDisableUser]\n    }, {\n      name: \"Bulk Enable/Disable Users Status\",\n      path: \"/encollect/users/v1/bulk-upload-enable-disable-status\",\n      acm: [ACMP.CanSearchBulkEnableDisableUserStatus]\n    }]\n  }]\n},\n// {\n//   name: \"Allocation\",\n//   icon: \"nav_allocation_icon\",\n//   subMenus: [\n//     {\n//       name: \"Agency Bulk Upload\",\n//       subMenus: [\n//         {\n//           name: \"Agency Bulk Allocation\",\n//           path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\n//           acm: [ACM.AlUploadAgencyAllocationBatchScreen],\n//         },\n//         {\n//           name: \"Agency Bulk Deallocation\",\n//           path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\n//           acm: [ACM.AlUploadAgencyUnallocationBatchScreen],\n//         },\n//         {\n//           name: \"Agency Allocation Status\",\n//           path: \"encollect/allocation/v1/primary-allocation-status\",\n//           acm: [ACM.AlPrimaryAllocationStatusScreen],\n//         },\n//         {\n//           name: \"Agency Deallocation Status\",\n//           path: \"encollect/allocation/v1/primary-unallocation-status\",\n//           acm: [ACM.AlPrimaryUnallocationStatusScreen],\n//         },\n//       ],\n//     },\n//     {\n//       name: \"Agent Bulk Upload\",\n//       subMenus: [\n//         {\n//           name: \"Agent Bulk Allocation\",\n//           path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlUploadCollectorAllocationBatchScreen],\n//         },\n//         {\n//           name: \"Agent Bulk Deallocation\",\n//           path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlUploadCollectorUnallocationBatchScreen],\n//         },\n//         {\n//           name: \"Agent Allocation Status\",\n//           path: \"encollect/allocation/v1/secondary-allocation-status\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlSecondaryAllocationStatusScreen],\n//         },\n//         {\n//           name: \"Agent Deallocation Status\",\n//           path: \"encollect/allocation/v1/secondary-unallocation-status\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlSecondaryUnallocationStatusScreen],\n//         },\n//       ],\n//     },\n//     {\n//       name: \"Allocation Owner Bulk Upload\",\n//       subMenus: [\n//         {\n//           name: \"Allocation Owner Upload\",\n//           path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlUploadAgencyAllocationOwnerScreen],\n//         },\n//         {\n//           name: \"Allocation Owner Upload Status\",\n//           path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlUploadAgencyAllocationOwnerStatusScreen],\n//         },\n//       ],\n//     },\n//     {\n//       name: \"Allocation Filters\",\n//       subMenus: [\n//         {\n//           name: \"Agency Allocation by Filters\",\n//           path: \"encollect/allocation/v1/primary-allocation-filters\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlPrimaryAllocationFiltersScreen],\n//         },\n//         {\n//           name: \"Agent Allocation by Filters\",\n//           path: \"encollect/allocation/v1/secondary-allocation-filters\",\n//           icon: \"far fa-id-badge\",\n//           acm: [ACM.AlSecondaryAllocationFiltersScreen],\n//         },\n//       ],\n//     },\n//   ],\n// },\n{\n  name: \"Allocation\",\n  icon: \"nav_allocation_icon\",\n  subMenus: [{\n    name: \"Agency Bulk Upload\",\n    subMenus: [{\n      name: \"Agency Bulk Allocation Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\n      acm: [ACMP.CanUploadPrimaryAllocationBatch]\n    }, {\n      name: \"Agency Bulk Allocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-batch-customerLevel\",\n      acm: [ACMP.CanUploadPrimaryAllocationBatch]\n    }, {\n      name: \"Agency Bulk Deallocation Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\n      acm: [ACMP.CanUploadPrimaryDeAllocationBatch]\n    }, {\n      name: \"Agency Bulk Deallocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-unallocation-batch-customerLevel\",\n      acm: [ACMP.CanUploadPrimaryDeAllocationBatch]\n    }, {\n      name: \"Agency Allocation Status\",\n      path: \"encollect/allocation/v1/primary-allocation-status\",\n      acm: [ACMP.CanSearchPrimaryAllocationBatchStatus]\n    }, {\n      name: \"Agency Deallocation Status\",\n      path: \"encollect/allocation/v1/primary-unallocation-status\",\n      acm: [ACMP.CanSearchPrimaryDeAllocationBatchStatus]\n    }]\n  }, {\n    name: \"Agent Bulk Upload\",\n    subMenus: [{\n      name: \"Agent Bulk Allocation Account Level\",\n      path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryAllocationBatch]\n    }, {\n      name: \"Agent Bulk Allocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-collector-allocation-batch-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryAllocationBatch]\n    }, {\n      name: \"Agent Bulk Deallocation Account Level\",\n      path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryDeAllocationBatch]\n    }, {\n      name: \"Agent Bulk Deallocation Customer Level\",\n      path: \"encollect/allocation/v1/upload-collector-unallocation-batch-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadSecondaryDeAllocationBatch]\n    }, {\n      name: \"Agent Allocation Status\",\n      path: \"encollect/allocation/v1/secondary-allocation-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchSecondaryAllocationBatchStatus]\n    }, {\n      name: \"Agent Deallocation Status\",\n      path: \"encollect/allocation/v1/secondary-unallocation-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchSecondaryDeAllocationBatchStatus]\n    }]\n  }, {\n    name: \"Allocation Owner Bulk Upload\",\n    subMenus: [{\n      name: \"Allocation Owner Bulk Upload Account Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadAllocationOwnerBatch]\n    }, {\n      name: \"Allocation Owner Bulk Upload Customer Level\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner-customerLevel\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUploadAllocationOwnerBatch]\n    }, {\n      name: \"Allocation Owner Upload Status\",\n      path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanSearchAllocationOwnerBatchStatus]\n    }]\n  }, {\n    name: \"Allocation Filters\",\n    subMenus: [{\n      name: \"Agency Allocation by Filters\",\n      path: \"encollect/allocation/v1/primary-allocation-filters\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUpdatePrimaryAllocationByFilter]\n    }, {\n      name: \"Agent Allocation by Filters\",\n      path: \"encollect/allocation/v1/secondary-allocation-filters\",\n      icon: \"far fa-id-badge\",\n      acm: [ACMP.CanUpdateSecondaryAllocationByFilter]\n    }]\n  }]\n}, {\n  name: \"Trails\",\n  icon: \"nav_trails_icon\",\n  subMenus: [{\n    name: \"Bulk Trail Upload\",\n    path: \"encollect/allocation/v1/bulk-trail\",\n    icon: \"fa fa-upload\",\n    acm: [ACMP.CanUploadBulkTrail]\n  }, {\n    name: \"Trail Upload Status\",\n    path: \"encollect/allocation/v1/bulk-trail-status\",\n    icon: \"fa fa-upload\",\n    acm: [ACMP.CanSearchBulkTrailStatus]\n  }]\n}, {\n  name: \"Payments\",\n  icon: \"nav_payments_icon\",\n  subMenus: [{\n    name: \"Receive Money from Collector\",\n    path: \"encollect/payments/v1/receive-money-from-collector\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanAcknowledgeReceipt]\n  }, {\n    name: \"Batch of Payments\",\n    subMenus: [{\n      name: \"Create Batch of Payments\",\n      path: \"encollect/payments/v1/create-batch-of-payments\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateBatch]\n    }, {\n      name: \"Search and Print Batch\",\n      path: \"encollect/payments/v1/print-batch-list\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchBatch, ACMP.CanPrintBatch]\n    }, {\n      name: \"Search and Edit Batch\",\n      path: \"encollect/payments/v1/search-and-edit-batch\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchBatch, ACMP.CanUpdateBatch]\n    }, {\n      name: \"Receive Batch of Payments at Branch\",\n      path: \"encollect/payments/v1/receive-batch-of-payments\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanAcknowledgeBatch]\n    }]\n  }, {\n    name: \"Deposit Slip\",\n    subMenus: [{\n      name: \"Create Deposit Slip\",\n      path: \"encollect/payments/v1/create-pay-slip\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreatePIS]\n    }, {\n      name: \"Search and View Deposit Slip\",\n      path: \"encollect/payments/v1/search-and-view-pay-in-slip\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSearchPIS]\n    }, {\n      name: \"Acknowledge Deposit Slip\",\n      path: \"encollect/payments/v1/central_ops_acknowledging\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanAcknowledgePIS]\n    }]\n  }, {\n    name: \"Receipts\",\n    subMenus: [{\n      name: \"Issue Receipt to Walk-in Customer\",\n      path: \"encollect/payments/v1/Walkin-customer-receipt\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateWalkinReceipt]\n    }, {\n      name: \"Send Duplicate Receipt\",\n      path: \"encollect/payments/v1/search-and-send-duplicate-email-e-receipt-and-SMS\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanSendDuplicateReceipt]\n    }, {\n      name: \"Raise Receipt Cancellation Request\",\n      path: \"encollect/payments/v1/reciept-cancellation-request\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanCreateReceiptCancellationRequest]\n    }, {\n      name: \"Action Receipt Cancellation Request\",\n      path: \"encollect/payments/v1/reciept-cancellation-request-approval-reject\",\n      icon: \"fa fa-credit-card\",\n      acm: [ACMP.CanApproveReceiptCancellationRequest, ACMP.CanRejectReceiptCancellationRequest]\n    }]\n  }, {\n    name: \"Bulk Payments Upload\",\n    subMenus: [{\n      name: \"Bulk Payments Upload\",\n      path: \"encollect/payments/v1/bulk-payments\",\n      icon: \"fa fa-upload\",\n      acm: [ACMP.CanCreatePIS]\n    }, {\n      name: \"Bulk Payments Upload Status\",\n      path: \"encollect/payments/v1/bulk-payments-upload-status\",\n      icon: \"fa fa-upload\",\n      acm: [ACMP.CanCreatePIS]\n    }]\n  }, {\n    name: \"Download Payment Report\",\n    path: \"encollect/payments/v1/download-payment-report\",\n    iconFa: \"fa fa-credit-card\",\n    acm: [ACMP.CanDownloadPaymentReport]\n  }]\n}, {\n  name: \"Geo Report\",\n  icon: \"nav_geo_report_icon\",\n  subMenus: [{\n    name: \"User Travel Report\",\n    path: \"travel-report/travel-report\",\n    icon: \"fa fa-users\",\n    acm: [ACMP.CanSearchTravelReport]\n  }]\n}, {\n  name: \"Digital ID\",\n  icon: \"nav_digital_id_icon\",\n  subMenus: [{\n    name: \"Digital ID Card\",\n    path: \"digital/card\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewDigitalIDCard]\n  }]\n}, {\n  name: \"Settlement\",\n  icon: \"nav_settlement_icon\",\n  subMenus: [{\n    name: \"Find Eligible Cases\",\n    path: \"settlement/eligible-cases\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanFlagSettlementAsEligible] //CanViewDigitalIDCard\n  }, {\n    name: \"Request Settlement\",\n    path: \"settlement/request-settlement\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanRequestSettlement]\n  }, {\n    name: \"My Requests\",\n    path: \"settlement/my-settlement-summary\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewMySettlement]\n  }, {\n    name: \"My Action Queue\",\n    path: \"settlement/my-settlement-queue-summary\",\n    icon: \"fa fa-credit-card\",\n    acm: [ACMP.CanViewMyQueueSettlement]\n  }]\n}, {\n  name: \"Target Setting\",\n  icon: \"nav_target_setting_icon\",\n  hide: true,\n  subMenus: [{\n    name: \"Upload Targets\",\n    path: \"target/upload-budgeted-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Upload Budgeted Target Status\",\n    path: \"target/budgeted-target-file-upload-status\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"View Budgeted Targets\",\n    path: \"target/view-budgeted-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Create Target\",\n    path: \"target/create-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }, {\n    name: \"Target Listing\",\n    path: \"target/search-target\",\n    icon: \"fa fa-credit-card\",\n    acm: []\n  }]\n}, {\n  name: \"Curing Tools\",\n  icon: \"nav_curing_tools_icon\",\n  hide: true,\n  subMenus: [{\n    name: \"Request Settlement\",\n    path: \"settlement/acs-request-settlement\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Requests\",\n    path: \"settlement/acs-mysettlement\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Settlement Queue\",\n    path: \"settlement/acs-settlement-queue\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Request Cure\",\n    path: \"encollect/cure/request-cure\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Cure\",\n    path: \"encollect/cure/my-cure\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Cure Queue\",\n    path: \"encollect/cure/my-cure-queue\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Add/Initiate Legal Case\",\n    path: \"encollect/legal-custom/create-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Legal Case\",\n    path: \"encollect/legal-custom/my-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Legal Queue\",\n    path: \"encollect/legal-custom/myqueue-legal\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Bulk Upload of Hearing Date\",\n    path: \"encollect/legal-custom/bulkupload-hearing\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Status of Bulk Upload of Hearing Date\",\n    path: \"encollect/legal-custom/bulkupload-hearing-status\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Bulk Upload to Initiate Case\",\n    path: \"encollect/legal-custom/bulkupload-initiate\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Status of Bulk Upload Initiate Case\",\n    path: \"encollect/legal-custom/bulkupload-initiate-status\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"Initiate Legal Request\",\n    path: \"encollect/legal/initiate-legal-request\",\n    icon: \"fa fa-users\",\n    hide: true\n  }, {\n    name: \"My Legal Queue\",\n    path: \"encollect/legal/my-legal-queue\",\n    icon: \"fa fa-users\",\n    hide: true\n  }, {\n    name: \"Create Repossession\",\n    path: \"encollect/repossession/create-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Repossession\",\n    path: \"encollect/repossession/my-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }, {\n    name: \"My Queue Repossession\",\n    path: \"encollect/repossession/myqueue-repossession\",\n    icon: \"fa fa-users\",\n    acm: []\n  }]\n}, {\n  name: \"Segmentation & Treatment\",\n  icon: \"nav_segmentation_icon\",\n  subMenus: [{\n    name: \"Segmentation\",\n    subMenus: [{\n      name: \"Create Segment\",\n      path: \"segmentation/create-segmentation\",\n      acm: [ACMP.CanCreateSegment]\n    }, {\n      name: \"Search Segments\",\n      path: \"segmentation/search-segmentation\",\n      acm: [ACMP.CanSearchSegment]\n    }, {\n      name: \"Sequence Segments\",\n      path: \"segmentation/segmentation-sequence\",\n      acm: [ACMP.CanSequenceSegment]\n    }, {\n      name: \"Compare Segments\",\n      path: \"segmentation/compare-segmentation\",\n      acm: [ACMP.CanCompareSegment]\n    }]\n  }, {\n    name: \"Treatment\",\n    subMenus: [{\n      name: \"Create Treatment\",\n      path: \"treatment/create-treatment-step1\",\n      acm: [ACMP.CanCreateTreatment]\n    }, {\n      name: \"Search Treatments\",\n      path: \"treatment/search-treatment\",\n      acm: [ACMP.CanSearchTreatment]\n    }, {\n      name: \"Sequence Treatments\",\n      path: \"treatment/treatment-sequence\",\n      acm: [ACMP.CanSequenceTreatement]\n    }]\n  }, {\n    name: \"Clear Segment / Treatment\",\n    path: \"segmentation/clear\",\n    acm: [ACMP.CanClearSegmentAndTreatementStamping]\n  }, {\n    name: \"Customer Search\",\n    path: \"coming-soon\",\n    hide: true\n  }, {\n    name: \"Account Search\",\n    path: \"coming-soon\",\n    hide: true\n  }]\n}, {\n  name: \"System Settings\",\n  icon: \"nav_system_settings_icon\",\n  subMenus: [{\n    name: \"Basic UI Settings\",\n    path: \"settings/main-settings\",\n    hide: true\n  }, {\n    name: \"Disposition Code Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Disposition Code Group\",\n      path: \"settings/disposition-group-config\"\n    }, {\n      name: \"Create Disposition Code\",\n      path: \"settings/disposition-code-config\"\n    }]\n  }, {\n    name: \"Deposit Bank Account Number Config\",\n    path: \"settings/disposition-account-number\",\n    hide: true\n  }, {\n    name: \"Allocations\",\n    hide: true,\n    subMenus: [{\n      name: \"Allocation Configuration\",\n      path: \"settings/allocation-config\"\n    }, {\n      name: \"Bucket Configuration\",\n      path: \"settings/allocation-bucket-config\"\n    }]\n  }, {\n    name: \"Payments\",\n    hide: true,\n    subMenus: [{\n      name: \"Offline Receipts Configuration\",\n      path: \"settings/payments/offline-receipt\"\n    }, {\n      name: \"Transaction Series Configuration\",\n      path: \"settings/payments/transaction-series\"\n    }, {\n      name: \"Mode of Payments Configuration\",\n      path: \"settings/payments/mode-of-payments\"\n    }, {\n      name: \"Issue Receipt Masters Configuration\",\n      path: \"settings/payments/denominations\"\n    }]\n  }, {\n    name: \"Geography Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Geography Master\",\n      path: \"settings/upload-geography-master\"\n    }, {\n      name: \"Upload Geography Master Status\",\n      path: \"settings/upload-geography-master-status\"\n    }, {\n      name: \"Search Geography Master\",\n      path: \"settings/geography-master-search\"\n    }]\n  }, {\n    name: \"Area Code Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Area Code Master\",\n      path: \"settings/upload-area-code-master\"\n    }, {\n      name: \"Upload Area Code Master Status\",\n      path: \"settings/upload-area-code-master-status\"\n    }, {\n      name: \"Search Area Code Master\",\n      path: \"settings/area-code-master-search\"\n    }]\n  }, {\n    name: \"Bank Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Bank Master\",\n      path: \"settings/upload-bank-master\"\n    }, {\n      name: \"Upload Bank Master Status\",\n      path: \"settings/upload-bank-master-status\"\n    }, {\n      name: \"Search Bank Master\",\n      path: \"settings/bank-master-search\"\n    }]\n  }, {\n    name: \"Account Upload\",\n    subMenus: [{\n      name: \"Bulk Account Upload\",\n      path: \"settings/upload-account-import-master\",\n      acm: [ACMP.CanUploadBulkAccounts]\n    }, {\n      name: \"Account Upload Status\",\n      path: \"settings/upload-account-import-master-status\",\n      acm: [ACMP.CanSearchBulkAccountsUploadStatus]\n    }]\n  }, {\n    name: \"Masters Upload\",\n    subMenus: [{\n      name: \"Bulk Upload Masters\",\n      path: \"settings/bulk-upload-master\",\n      acm: [ACMP.CanUploadMasters]\n    }, {\n      name: \"Masters Upload Status\",\n      path: \"settings/bulk-upload-master-status\",\n      acm: [ACMP.CanSearchUploadMastersStatus]\n    }, {\n      name: \"View and Disable Masters\",\n      path: \"settings/view-masters\",\n      acm: [ACMP.CanSearchMasters] //SSViewDisableMastersScreen\n    }]\n  }, {\n    name: \"Define ACM\",\n    subMenus: [{\n      name: \"Web\",\n      path: \"settings/define-web-acm\",\n      acm: [ACMP.CanDefineACM]\n    }, {\n      name: \"Mobile\",\n      path: \"settings/define-mobile-acm\",\n      acm: [ACMP.CanDefineACM]\n    }]\n  }, {\n    name: \"Permissions\",\n    subMenus: [{\n      name: \"Define Permission Schemes\",\n      path: \"settings/permissions/define-permission-group\",\n      acm: [ACMP.CanCreatePermissionScheme]\n    }, {\n      name: \"Search Permission Schemes\",\n      path: \"settings/permissions/search-permission-groups\",\n      acm: [ACMP.CanViewPermissionSchemes]\n    }, {\n      name: \"Assign Permission Scheme to Designations\",\n      path: \"settings/permissions/assign-designations-to-permission-groups\",\n      acm: [ACMP.CanViewDesignationSchemeDetails]\n    }, {\n      name: \"Search Permissions\",\n      path: \"settings/permissions/search-permissions\",\n      acm: [ACMP.CanSearchPermissions]\n    }]\n  }, {\n    name: \"Account Detail Label Customization\",\n    path: \"settings/account-detail-label-customization\",\n    roles: [],\n    hide: true\n  }, {\n    name: \"Base Branch Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Upload Base Branch Master\",\n      path: \"settings/upload-branch-master\"\n    }, {\n      name: \"Upload Base Branch Master Status\",\n      path: \"settings/upload-branch-master-status\"\n    }, {\n      name: \"Search Base Branch Master\",\n      path: \"settings/branch-master-search\"\n    }]\n  }, {\n    name: \"Workflow\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Workflow\",\n      path: \"/workflows\"\n    }, {\n      name: \"Edit Workflow\",\n      path: \"/workflows/edit-workflow\"\n    }, {\n      name: \"View Workflow\",\n      path: \"/workflows/view-workflow\"\n    }, {\n      name: \"Search Workflow\",\n      path: \"/workflows/search-workflow\"\n    }]\n  }, {\n    name: \"Action\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Action\",\n      path: \"/action/create-action\"\n    }, {\n      name: \"Edit Action\",\n      path: \"/action/edit-action\"\n    }, {\n      name: \"View Action\",\n      path: \"/action/view-action\"\n    }, {\n      name: \"Search Action\",\n      path: \"/action/search-action\"\n    }]\n  }, {\n    name: \"Value\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Value\",\n      path: \"/value/create-value\"\n    }, {\n      name: \"Edit Value\",\n      path: \"/value/edit-value\"\n    }, {\n      name: \"View Value\",\n      path: \"/value/view-value\"\n    }, {\n      name: \"Search Value\",\n      path: \"/value/search-value\"\n    }]\n  }, {\n    name: \"Product Master\",\n    hide: true,\n    subMenus: [{\n      name: \"Configure Product Group\",\n      path: \"settings/product-group-config\"\n    }, {\n      name: \"Configure Product\",\n      path: \"settings/product-config\"\n    }, {\n      name: \"Configure Sub Product\",\n      path: \"settings/sub-product-config\"\n    }]\n  }, {\n    name: \"Account Search Scope\",\n    path: \"settings/account-search-scope\",\n    roles: [\"SYSTEMADMIN\", \"BankToBackEndInternalBIHP\"]\n    // acm: [ACM.SSAccountSearchScopeScreen],\n  }]\n}, {\n  name: \"Communication\",\n  icon: \"nav_communication_icon\",\n  subMenus: [{\n    name: \"Search Communication Templates\",\n    path: \"communication/search-communication-templates\",\n    hasAccess: true\n  }, {\n    name: \"Create Communication Template\",\n    path: \"communication/create-communication-template\",\n    hasAccess: true\n  }, {\n    name: \"Search Communication Triggers\",\n    path: \"communication/search-communication-triggers\",\n    hasAccess: true\n  }, {\n    name: \"Create Communication Trigger\",\n    path: \"communication/create-communication-trigger\",\n    hasAccess: true\n  }, {\n    name: \"Communication Template\",\n    subMenus: [{\n      name: \"Create Communication Template\",\n      path: \"communication/create-template\"\n      // acm: [ACMP.CanCreateCommunicationTemplate],\n    }, {\n      name: \"Search Communication Template\",\n      path: \"communication/search-template\",\n      acm: [ACMP.CanSearchCommunicationTemplate]\n    }]\n  }, {\n    name: \"Communication Task\",\n    hide: true,\n    subMenus: [{\n      name: \"Create Communication Task\",\n      path: \"communication/create-task\",\n      acm: []\n    }, {\n      name: \"Search Communication Task\",\n      path: \"communication/search-task\",\n      acm: []\n    }]\n  }\n  // {\n  //   name: \"Upload Letter\",\n  //   subMenus: [\n  //     {\n  //       name: \"Upload Letter Status\",\n  //       path: \"communication/Upload-letter-status\",\n  //       acm: [],\n  //     },\n  //     {\n  //       name: \"View Upload Letter File Status\",\n  //       path: \"communication/view-Upload-letter-status\",\n  //       acm: [],\n  //     },\n  //   ],\n  // },\n  // {\n  //   name: \"Broadcast\",\n  //   path: \"communication/broadcast\",\n  //   acm: [],\n  // },\n  ]\n}, {\n  name: \"Reports\",\n  icon: \"nav_reports_icon\",\n  subMenus: [{\n    name: \"Allocation Reports\",\n    subMenus: [{\n      name: \"Agency Allocation Gap Report\",\n      path: \"reports/agency-gap-mis-report\",\n      acm: [ACMP.CanViewAgencyAllocationGapReport]\n    }, {\n      name: \"Agent Allocation Gap Report\",\n      path: \"reports/agent-gap-mis-report\",\n      acm: [ACMP.CanViewAgentAllocationGapReport]\n    }, {\n      name: \"Allocated vs Achieved Report\",\n      path: \"reports/allocated-vs-achieved-report\",\n      acm: [ACMP.CanViewAllocatedvsArchievedReport]\n    }]\n  }, {\n    name: \"Trail Reports\",\n    subMenus: [{\n      name: \"Trail Gap Report\",\n      path: \"reports/trail-gap-report\",\n      acm: [ACMP.CanViewTrailGapReport]\n    }, {\n      name: \"Trail History Report\",\n      path: \"reports/trail-history-report\",\n      acm: [ACMP.CanViewTrailHistoryReport]\n    }, {\n      name: \"Trail Intensity Report\",\n      path: \"reports/trail-intensity-report\",\n      acm: [ACMP.CanViewTrailIntensityReport]\n    }]\n  }, {\n    name: \"Payment Report\",\n    path: \"reports/payment-report\",\n    acm: [ACMP.CanViewPaymentReport]\n  }, {\n    name: \"Money Movement Report\",\n    path: \"reports/money-movement-report\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }, {\n    name: \"Attendance Report\",\n    path: \"attendance/attendance-report\",\n    acm: [ACMP.CanViewAttendanceReport]\n  }, {\n    name: \"Communication History Report\",\n    path: \"reports/communication-history-report\",\n    acm: [ACMP.CanViewCommunicationHistoryReport]\n  }, {\n    name: \"Account Dashboard Report\",\n    path: \"reports/account-dashboard-report\",\n    acm: [ACMP.CanViewAccountDashboardReport]\n  }, {\n    name: \"Performance Report\",\n    path: \"reports/performance-report\",\n    acm: [ACMP.CanViewPerformanceReport]\n  }, {\n    name: \"Supervisory Report\",\n    path: \"reports/supervisory-report\",\n    acm: [ACMP.CanViewSupervisoryReport]\n  }, {\n    name: \"Collection Intensity Report\",\n    path: \"reports/collection-intensity-report\",\n    acm: [ACMP.CanViewCollectionIntensityReport]\n  }, {\n    name: \"Collection Trend Report\",\n    path: \"reports/collection-trend-report\",\n    acm: [ACMP.CanViewCollectionTrendReport]\n  }, {\n    name: \"Visit Intensity Report\",\n    path: \"reports/visit-intensity-report\",\n    acm: [ACMP.CanViewVisitIntensityReport]\n  }, {\n    name: \"Collection Dashboard\",\n    path: \"reports/collection-dashboard\",\n    hide: false\n  }, {\n    name: \"Allocation vs Collections vs Trails Analysis Report\",\n    path: \"reports/allocation-collection-trails-report\",\n    hide: true\n  }, {\n    name: \"Target vs Actual Report\",\n    path: \"reports/target-actual-analysis-report\",\n    hide: true\n  }, {\n    name: \"Daily Legal Report\",\n    path: \"reports/daily-legal-report\",\n    hide: true\n  }, {\n    name: \"Daily Repossession Report\",\n    path: \"reports/daily-repo-report\",\n    hide: true\n  }, {\n    name: \"Allocation vs Collections vs Trails Analysis Report Sample\",\n    path: \"demo-reports/allocation-collection-trails-report\",\n    hide: true\n  }, {\n    name: \"Supervisory Report Sample\",\n    path: \"demo-reports/supervisory-report\",\n    hide: true\n  }, {\n    name: \"Collection Dashboard Sample\",\n    path: \"demo-reports/collection-dashboard\",\n    hide: true\n  }, {\n    name: \"Customer Contact Report\",\n    path: \"reports/ccd-report\",\n    acm: [ACMP.CanViewCustomerContactReport]\n  }, {\n    name: \"Cash Wallet Limit Report\",\n    path: \"reports/cash-wallet-limit-report\",\n    acm: [ACMP.CanViewCashWalletLimitReport] //CanViewCashWalletLimitReport\n  }]\n}, {\n  name: \"Insights\",\n  icon: \"nav_insights_icon\",\n  subMenus: [{\n    name: \"Primary Allocation Insights\",\n    path: \"insights/primary-allocation-insights\",\n    acm: [ACMP.CanViewAgencyAllocationGapReport]\n  }, {\n    name: \"Secondary Allocation Insights\",\n    path: \"insights/secondary-allocation-insights\",\n    acm: [ACMP.CanViewAgentAllocationGapReport]\n  }, {\n    name: \"Trail Gap Insights\",\n    path: \"insights/trail-gap-insights\",\n    acm: [ACMP.CanViewTrailGapReport]\n  }, {\n    name: \"Allocated vs Achieved Insights\",\n    path: \"insights/allocated-vs-achieved-insights\",\n    acm: [ACMP.CanViewAllocatedvsArchievedReport]\n  }, {\n    name: \"Money Movement Insights (Agency Staff)\",\n    path: \"insights/money-movement-insights-agency-staff\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }, {\n    name: \"Money Movement Insights (Bank Staff)\",\n    path: \"insights/money-movement-insights-bank-staff\",\n    acm: [ACMP.CanViewMoneyMovementReport]\n  }]\n}];", "map": {"version": 3, "names": ["Component", "inject", "NavigationCancel", "NavigationEnd", "NavigationError", "NavigationStart", "Router", "interval", "map", "UserService", "ACMP", "AcmService", "ToastrService", "JWTTokenService", "leftMenuMode", "localStorage", "getItem", "MenuLayoutComponent", "constructor", "router", "userService", "acmService", "toastr", "jwtTokenService", "currentTime$", "pipe", "Date", "isLoading", "isSideNavExpanded", "profileImage", "logoUrl", "permissions", "isAuthenticated$", "isAuthenticated", "fetchCurrentUser", "subscribe", "isUpdated", "buildNavList", "getPermissions", "events", "event", "currentUser", "userData", "primaryRole", "roles", "find", "o", "isPrimaryDesignation", "loadProfileImage", "getLogoImage", "toggleSideNav", "setItem", "hoverSideNav", "value", "onSelectMenuItem", "item", "items", "subMenus", "length", "for<PERSON>ach", "name", "expanded", "path", "navigate", "config<PERSON>av", "navList", "reduce", "list", "children", "hasAccess", "some", "class", "toLowerCase", "replace", "acm", "hasACMAccess", "hasRoleAccess", "push", "MENU_LIST", "ngOnDestroy", "filePreview", "res", "mediaType", "arrayBufferView", "Uint8Array", "file", "Blob", "type", "my<PERSON><PERSON><PERSON>", "FileReader", "onloadend", "e", "result", "readAsDataURL", "user", "id", "get<PERSON>ogo", "err", "error", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "icon", "CanSearchAccounts", "CanCreateAgency", "CanSearchAgency", "CanCreateAgent", "CanSearchAgent", "CanCreateStaff", "CanSearchStaff", "hide", "CanUploadBulkUser", "CanSearchBulkUserUploadStatus", "CanUploadBulkEnableDisableUser", "CanSearchBulkEnableDisableUserStatus", "CanUploadPrimaryAllocationBatch", "CanUploadPrimaryDeAllocationBatch", "CanSearchPrimaryAllocationBatchStatus", "CanSearchPrimaryDeAllocationBatchStatus", "CanUploadSecondaryAllocationBatch", "CanUploadSecondaryDeAllocationBatch", "CanSearchSecondaryAllocationBatchStatus", "CanSearchSecondaryDeAllocationBatchStatus", "CanUploadAllocationOwnerBatch", "CanSearchAllocationOwnerBatchStatus", "CanUpdatePrimaryAllocationByFilter", "CanUpdateSecondaryAllocationByFilter", "CanUploadBulkTrail", "CanSearchBulkTrailStatus", "CanAcknowledgeReceipt", "CanCreateBatch", "CanSearchBatch", "CanPrintBatch", "CanUpdateBatch", "CanAcknowledgeBatch", "CanCreatePIS", "CanSearchPIS", "CanAcknowledgePIS", "CanCreateWalkinReceipt", "CanSendDuplicateReceipt", "CanCreateReceiptCancellationRequest", "CanApproveReceiptCancellationRequest", "CanRejectReceiptCancellationRequest", "iconFa", "CanDownloadPaymentReport", "CanSearchTravelReport", "CanViewDigitalIDCard", "CanFlagSettlementAsEligible", "CanRequestSettlement", "CanViewMySettlement", "CanViewMyQueueSettlement", "CanCreateSegment", "CanSearchSegment", "CanSequenceSegment", "CanCompareSegment", "CanCreateTreatment", "CanSearchTreatment", "CanSequenceTreatement", "CanClearSegmentAndTreatementStamping", "CanUploadBulkAccounts", "CanSearchBulkAccountsUploadStatus", "CanUploadMasters", "CanSearchUploadMastersStatus", "CanSearchMasters", "CanDefineACM", "CanCreatePermissionScheme", "CanViewPermissionSchemes", "CanViewDesignationSchemeDetails", "CanSearchPermissions", "CanSearchCommunicationTemplate", "CanViewAgencyAllocationGapReport", "CanViewAgentAllocationGapReport", "CanViewAllocatedvsArchievedReport", "CanViewTrailGapReport", "CanViewTrailHistoryReport", "CanViewTrailIntensityReport", "CanViewPaymentReport", "CanViewMoneyMovementReport", "CanViewAttendanceReport", "CanViewCommunicationHistoryReport", "CanViewAccountDashboardReport", "CanViewPerformanceReport", "CanViewSupervisoryReport", "CanViewCollectionIntensityReport", "CanViewCollectionTrendReport", "CanViewVisitIntensityReport", "CanViewCustomerContactReport", "CanViewCashWalletLimitReport"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\shared\\components\\menu-layout\\menu-layout.component.ts"], "sourcesContent": ["import { Component, inject, OnDestroy } from \"@angular/core\";\r\nimport {\r\n  NavigationCancel,\r\n  NavigationEnd,\r\n  NavigationError,\r\n  NavigationStart,\r\n  Router,\r\n} from \"@angular/router\";\r\nimport { interval, map, Observable } from \"rxjs\";\r\nimport { UserService } from \"src/app/authentication/user.service\";\r\nimport { ACMP, AcmService } from \"../../services\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { JWTTokenService } from \"../../services/jwt-token.service\";\r\n\r\ntype LeftMenuMode = \"push\" | \"over\";\r\nconst leftMenuMode: LeftMenuMode = localStorage.getItem(\"leftMenuMode\") as LeftMenuMode || \"push\";\r\n\r\n@Component({\r\n  selector: \"app-menu-layout\",\r\n  templateUrl: \"./menu-layout.component.html\",\r\n  styleUrls: [\"./menu-layout.component.scss\"],\r\n})\r\nexport class MenuLayoutComponent implements OnDestroy {\r\n  private jwtTokenService = inject(JWTTokenService);\r\n\r\n  public currentTime$ = interval(1000).pipe(map(() => new Date()));\r\n  public isAuthenticated$: Observable<any>;\r\n  public currentUser: any;\r\n  public primaryRole: any;\r\n  public isLoading: boolean = false;\r\n\r\n  public navList: NavItem[];\r\n\r\n  public isSideNavExpanded: boolean = leftMenuMode === 'push';\r\n  public leftMenuMode: LeftMenuMode = leftMenuMode;\r\n  public profileImage:\r\n    | string\r\n    | ArrayBuffer = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAADmCAAAAADkAaZnAAAFCElEQVR42u3cV7LbPAwFYO1/i6C61ahiq1j2zV+Sh8xkEsdVBGyA5FkBviHFIlIKvjuRwDM90zM984PM/52IZ3qmZ3qmZ3qmZ3qmZ9rM/M+JeKZneqZneqZneqZnOsr814l4pmd6pmd6pmd6pmd6ps3Mf5yIZ3qmZ3qmU8zZAeaxjeFn8mp/tJH5NbdFCH8kLPqTVcxTk8L1xNX4ZQnzmMPd5OSNGnwjz1rB48RNm/YrWQ30TK3g6WSTUOaUwEvJzhKZGl6NWsQxlwSAh5OSOSjYEnUWxaxhY3aCmOcSNmcSwzwn25WQrUKYRkqA6CyCaagEAKUFMI2VACUu84sgJSAkmREromBqwEnPmjkCVmrGzJNCY0LLl5kBYvZcmT2mEtSJJxOzywIA7HgyS0DOyJF5xFZCxpGJ3pgAMz8mfmMiTZ4B98YEhcI8I2YBikwIlaEyGxJmw40ZkTAzZsyJRAmKGbOgYcLKixkRMQ+smER9lhuzpWI2rJiZG0xwgjmxZp6w0tExzYvDYzZuMMlGIBjcYB7cYI5uME9OMCM3mBkrZsF4PpEwbw6smJqKecRgrljZEylzjOLwmAsRs+fFXGnekaiVGTMnYZbcmDRj0IEbk2RfnazcmGtCwNT8mBSv9hZ+zIXppInMXJlOmrjMpWA6aaIyDxHbcXZdgyNWUpKVHlJxaMyRZHEQcWMORLswJ5iKG3MmYabcmEeSfVjDjrkjeRPNjklxIhYf2TEpem3DkElwYW9gyKzxma0bzBqNuaCFYKitsGpDZKaeaRGToNM2DJkHgu0mQyZBcy4cmRPyF0WQsWQuFTJT82QiN6daeDIXzXM6wWYumLuUGLGuYEZNH2MZs2pCrAuZOc89yv2gDLkqdOY8Y3Tcij8T4yil5s/EmD17N5iDG8zZCWbsBjN1g1kJYCL8x0KjMyf0IBzoDtg1ETAn8+XeJIFpfE86FsE8mO6ucxHMyfTKaSODaTrW9jKYk9nmWk1CmGYrhEQK0+wrhlIKcwpNmJ0YZsLr0aRimjycGQVzJInJ6VhDUA8RczS4VjIIYu43L/iSURBz3HqcojpRzHHjYFuNspg7Pl2WkLnx3/WVLOZ+6+q9FMXcvuXMBTFNNta5GKbZ64NaBnNv+oWjxmce0NOFhkpQHXZN+EyMU1x0JzZT49wlUZozc8D7/8pu4MocCsxbXmHNkomLRIYGbJEAAGExcGL2ZP9EUkXPhalToAzGYGTOrCMgjjLvuoHpIxnCG6IKU+beIP1OwZsS1iaF7g2YXQbvTFi0H2DqBN6fKCkqrfU7mF1dJCF8OCrJypaOWecfF/5mTauegNnlCrglrZGZH3kYnxmaKkQmV+ST0OeYXQqsE2oMZqmAe9LelNklICBKmzG1AhnJ7zGHBylBTJL+puIRMwNBibqNTFFKAKU3MYUpbzsDq5QAqn2ZKVB56/m8wyxAZKLXmA0ITf4Ks1NSmVC/wIzFKq8NQ4FdD+bPxH8z+6tplWQmlJeeG8xUtBJU+xSzBuHJnmLG0pmgn2CKb0yA9Amm/Ma8bM5rzMYCJeweMlMbmOoRU/iceXXuvMIsrVBC8oCZ2MGE9i6ztUT5R68NusuUtjDT31B/MzNbmOouM7KFCfU9pjVKKO4wK3uY2R3mzh5mfIeZ28MM7zBje5jgCFPfZkYWMavbTLCT2V7EJmb5S3XJbGxi5jeZlWfK26PcZBY2MeNfrB9mJmk0jeOmPQAAAABJRU5ErkJggg==`; // assets/new/svgs/profile_img.svg\r\n  public logoUrl: string | ArrayBuffer = \"\";\r\n\r\n  permissions = [];\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private acmService: AcmService,\r\n    private toastr: ToastrService\r\n  ) {\r\n    this.isAuthenticated$ = this.userService.isAuthenticated;\r\n    this.fetchCurrentUser();\r\n    this.isAuthenticated$.subscribe((isUpdated: boolean) => {\r\n      if (isUpdated) {\r\n        this.buildNavList();\r\n      }\r\n      this.permissions = this.jwtTokenService.getPermissions();\r\n    });\r\n    this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationStart) {\r\n        this.isLoading = true;\r\n      } else if (\r\n        event instanceof NavigationEnd ||\r\n        event instanceof NavigationCancel ||\r\n        event instanceof NavigationError\r\n      ) {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private fetchCurrentUser() {\r\n    this.userService.currentUser.subscribe((userData): any => {\r\n      this.currentUser = userData;\r\n      this.primaryRole = this.currentUser?.roles?.find(o => o?.isPrimaryDesignation) || this.currentUser?.roles?.[0];\r\n      this.loadProfileImage(userData?.profileImage);\r\n      this.getLogoImage(userData);\r\n    });\r\n  }\r\n\r\n  toggleSideNav() {\r\n    this.leftMenuMode = this.leftMenuMode === 'push' ? 'over' : 'push';\r\n    this.isSideNavExpanded = this.leftMenuMode === 'push';\r\n    localStorage.setItem(\"leftMenuMode\", `${this.leftMenuMode}`);\r\n  }\r\n\r\n  hoverSideNav(value: boolean) {\r\n    if (this.leftMenuMode === 'over') {\r\n      this.isSideNavExpanded = value;\r\n    }\r\n  }\r\n\r\n  onSelectMenuItem(item: any, items: any[]) {\r\n    if (item?.subMenus?.length) {\r\n      items.forEach((o) => {\r\n        if (item.name !== o.name) o[\"expanded\"] = false;\r\n      });\r\n      item[\"expanded\"] = !item?.expanded;\r\n    } else if (item?.path) {\r\n      this.router.navigate([item?.path]);\r\n    }\r\n  }\r\n\r\n  buildNavList() {\r\n    const configNav = (navList: NavItem[]) =>\r\n      navList.reduce((list: NavItem[], item: NavItem) => {\r\n        if (item?.subMenus?.length) {\r\n          const children = configNav(item?.subMenus);\r\n          item.subMenus = children;\r\n          item.hasAccess = children.some((item: NavItem) => item.hasAccess);\r\n          item.class = item?.name\r\n            ?.toLowerCase()\r\n            ?.replace(/ & /g, \"-\")\r\n            ?.replace(/ /g, \"-\");\r\n        } else if (item?.acm?.length) {\r\n          item.hasAccess = this.acmService.hasACMAccess(item?.acm);\r\n        } else if (item?.roles?.length) {\r\n          item.hasAccess = this.acmService.hasRoleAccess(item?.roles);\r\n        } else {\r\n          item.hasAccess = item?.hasAccess ?? false;\r\n        }\r\n        delete item?.acm;\r\n        list.push(item);\r\n        return list;\r\n      }, []);\r\n    const navList = configNav(MENU_LIST());\r\n    this.navList = navList;\r\n  }\r\n\r\n  ngOnDestroy(): void {}\r\n\r\n  private loadProfileImage(profileImage: string) {\r\n    if (profileImage) {\r\n      this.userService.filePreview(profileImage).subscribe((res) => {\r\n        const mediaType = \"image/jpeg\";\r\n        const arrayBufferView = new Uint8Array(res);\r\n        const file = new Blob([arrayBufferView], { type: mediaType });\r\n        const myReader: FileReader = new FileReader();\r\n        myReader.onloadend = (e) => {\r\n          this.profileImage = myReader.result;\r\n        };\r\n        myReader.readAsDataURL(file);\r\n      });\r\n    }\r\n  }\r\n\r\n  private getLogoImage(user: any) {\r\n    if (!user?.id) return;\r\n    this.userService.getLogo().subscribe(\r\n      (res) => {\r\n        const mediaType = \"image/jpeg\";\r\n        const arrayBufferView = new Uint8Array(res);\r\n        const file = new Blob([arrayBufferView], { type: mediaType });\r\n        const myReader: FileReader = new FileReader();\r\n        myReader.onloadend = (e) => {\r\n          this.logoUrl = myReader.result;\r\n        };\r\n        myReader.readAsDataURL(file);\r\n      },\r\n      (err) => {\r\n        this.toastr.error(\"Logo is not found\");\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\ninterface NavItem {\r\n  name: String;\r\n  icon?: String;\r\n  iconImage?: String;\r\n  path?: String;\r\n  subMenus?: NavItem[];\r\n  hide?: boolean;\r\n  acm?: ACMP[];\r\n  roles?: string[];\r\n  hasAccess?: boolean;\r\n  class?: string;\r\n}\r\n\r\nconst MENU_LIST = () => [\r\n  {\r\n    name: \"Account Search\",\r\n    icon: \"nav_dash_icon\",\r\n    path: \"/encollect/v1/dashboard\",\r\n    // hasAccess: true,\r\n    acm: [ACMP.CanSearchAccounts],\r\n  },\r\n  {\r\n    name: \"User Management\",\r\n    icon: \"nav_user_mgmt_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Agency Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Agency\",\r\n            path: \"/encollect/agency/v1/agencyEmpanelment-create\",\r\n            acm: [ACMP.CanCreateAgency],\r\n          },\r\n          {\r\n            name: \"Search Agency\",\r\n            path: \"/encollect/agency/v1/agencyEmpanelment-search\",\r\n            acm: [ACMP.CanSearchAgency],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Agent Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Agent\",\r\n            path: \"/encollect/agent/v1/create\",\r\n            acm: [ACMP.CanCreateAgent],\r\n          },\r\n          {\r\n            name: \"Search Agent\",\r\n            path: \"/encollect/agent/v1/search\",\r\n            acm: [ACMP.CanSearchAgent],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Staff Empanelment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Add Staff\",\r\n            path: \"/encollect/staff/v1/create-collection-staff\",\r\n            acm: [ACMP.CanCreateStaff],\r\n          },\r\n          {\r\n            name: \"Search Staff\",\r\n            path: \"/encollect/staff/v1/search-collection-staff\",\r\n            acm: [ACMP.CanSearchStaff],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Locked Profiles\",\r\n        path: \"/encollect/users/v1/locked-profiles\",\r\n        hide: true,\r\n        // acm: [ACM.UMLockedProfilesScreen], // TODO: ACM Configuration\r\n      },\r\n      {\r\n        name: \"Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"User Creation Upload\",\r\n            path: \"/encollect/users/v1/bulk-user-upload\",\r\n            acm: [ACMP.CanUploadBulkUser],\r\n          },\r\n          {\r\n            name: \"User Creation Upload Status\",\r\n            path: \"/encollect/users/v1/bulk-user-upload-status\",\r\n            acm: [ACMP.CanSearchBulkUserUploadStatus],\r\n          },\r\n          {\r\n            name: \"Bulk Enable/Disable Users\",\r\n            path: \"/encollect/users/v1/bulk-upload-enable-disable\",\r\n            acm: [ACMP.CanUploadBulkEnableDisableUser],\r\n          },\r\n          {\r\n            name: \"Bulk Enable/Disable Users Status\",\r\n            path: \"/encollect/users/v1/bulk-upload-enable-disable-status\",\r\n            acm: [ACMP.CanSearchBulkEnableDisableUserStatus],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n  // {\r\n  //   name: \"Allocation\",\r\n  //   icon: \"nav_allocation_icon\",\r\n  //   subMenus: [\r\n  //     {\r\n  //       name: \"Agency Bulk Upload\",\r\n  //       subMenus: [\r\n  //         {\r\n  //           name: \"Agency Bulk Allocation\",\r\n  //           path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\r\n  //           acm: [ACM.AlUploadAgencyAllocationBatchScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agency Bulk Deallocation\",\r\n  //           path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\r\n  //           acm: [ACM.AlUploadAgencyUnallocationBatchScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agency Allocation Status\",\r\n  //           path: \"encollect/allocation/v1/primary-allocation-status\",\r\n  //           acm: [ACM.AlPrimaryAllocationStatusScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agency Deallocation Status\",\r\n  //           path: \"encollect/allocation/v1/primary-unallocation-status\",\r\n  //           acm: [ACM.AlPrimaryUnallocationStatusScreen],\r\n  //         },\r\n  //       ],\r\n  //     },\r\n  //     {\r\n  //       name: \"Agent Bulk Upload\",\r\n  //       subMenus: [\r\n  //         {\r\n  //           name: \"Agent Bulk Allocation\",\r\n  //           path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlUploadCollectorAllocationBatchScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agent Bulk Deallocation\",\r\n  //           path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlUploadCollectorUnallocationBatchScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agent Allocation Status\",\r\n  //           path: \"encollect/allocation/v1/secondary-allocation-status\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlSecondaryAllocationStatusScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agent Deallocation Status\",\r\n  //           path: \"encollect/allocation/v1/secondary-unallocation-status\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlSecondaryUnallocationStatusScreen],\r\n  //         },\r\n  //       ],\r\n  //     },\r\n  //     {\r\n  //       name: \"Allocation Owner Bulk Upload\",\r\n  //       subMenus: [\r\n  //         {\r\n  //           name: \"Allocation Owner Upload\",\r\n  //           path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlUploadAgencyAllocationOwnerScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Allocation Owner Upload Status\",\r\n  //           path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlUploadAgencyAllocationOwnerStatusScreen],\r\n  //         },\r\n  //       ],\r\n  //     },\r\n  //     {\r\n  //       name: \"Allocation Filters\",\r\n  //       subMenus: [\r\n  //         {\r\n  //           name: \"Agency Allocation by Filters\",\r\n  //           path: \"encollect/allocation/v1/primary-allocation-filters\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlPrimaryAllocationFiltersScreen],\r\n  //         },\r\n  //         {\r\n  //           name: \"Agent Allocation by Filters\",\r\n  //           path: \"encollect/allocation/v1/secondary-allocation-filters\",\r\n  //           icon: \"far fa-id-badge\",\r\n  //           acm: [ACM.AlSecondaryAllocationFiltersScreen],\r\n  //         },\r\n  //       ],\r\n  //     },\r\n  //   ],\r\n  // },\r\n  {\r\n    name: \"Allocation\",\r\n    icon: \"nav_allocation_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Agency Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Bulk Allocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-batch\",\r\n            acm: [ACMP.CanUploadPrimaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Allocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-batch-customerLevel\",\r\n            acm: [ACMP.CanUploadPrimaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Deallocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-unallocation-batch\",\r\n            acm: [ACMP.CanUploadPrimaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Bulk Deallocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-unallocation-batch-customerLevel\",\r\n            acm: [ACMP.CanUploadPrimaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agency Allocation Status\",\r\n            path: \"encollect/allocation/v1/primary-allocation-status\",\r\n            acm: [ACMP.CanSearchPrimaryAllocationBatchStatus],\r\n          },\r\n          {\r\n            name: \"Agency Deallocation Status\",\r\n            path: \"encollect/allocation/v1/primary-unallocation-status\",\r\n            acm: [ACMP.CanSearchPrimaryDeAllocationBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Agent Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agent Bulk Allocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-allocation-batch\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Allocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-allocation-batch-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Deallocation Account Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-unallocation-batch\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Bulk Deallocation Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-collector-unallocation-batch-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadSecondaryDeAllocationBatch],\r\n          },\r\n          {\r\n            name: \"Agent Allocation Status\",\r\n            path: \"encollect/allocation/v1/secondary-allocation-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchSecondaryAllocationBatchStatus],\r\n          },\r\n          {\r\n            name: \"Agent Deallocation Status\",\r\n            path: \"encollect/allocation/v1/secondary-unallocation-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchSecondaryDeAllocationBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Allocation Owner Bulk Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Allocation Owner Bulk Upload Account Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadAllocationOwnerBatch],\r\n          },\r\n          {\r\n            name: \"Allocation Owner Bulk Upload Customer Level\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner-customerLevel\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUploadAllocationOwnerBatch],\r\n          },\r\n          {\r\n            name: \"Allocation Owner Upload Status\",\r\n            path: \"encollect/allocation/v1/upload-agency-allocation-owner-status\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanSearchAllocationOwnerBatchStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Allocation Filters\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Allocation by Filters\",\r\n            path: \"encollect/allocation/v1/primary-allocation-filters\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUpdatePrimaryAllocationByFilter],\r\n          },\r\n          {\r\n            name: \"Agent Allocation by Filters\",\r\n            path: \"encollect/allocation/v1/secondary-allocation-filters\",\r\n            icon: \"far fa-id-badge\",\r\n            acm: [ACMP.CanUpdateSecondaryAllocationByFilter],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Trails\",\r\n    icon: \"nav_trails_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Bulk Trail Upload\",\r\n        path: \"encollect/allocation/v1/bulk-trail\",\r\n        icon: \"fa fa-upload\",\r\n        acm: [ACMP.CanUploadBulkTrail],\r\n      },\r\n      {\r\n        name: \"Trail Upload Status\",\r\n        path: \"encollect/allocation/v1/bulk-trail-status\",\r\n        icon: \"fa fa-upload\",\r\n        acm: [ACMP.CanSearchBulkTrailStatus],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Payments\",\r\n    icon: \"nav_payments_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Receive Money from Collector\",\r\n        path: \"encollect/payments/v1/receive-money-from-collector\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanAcknowledgeReceipt],\r\n      },\r\n      {\r\n        name: \"Batch of Payments\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Batch of Payments\",\r\n            path: \"encollect/payments/v1/create-batch-of-payments\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateBatch],\r\n          },\r\n          {\r\n            name: \"Search and Print Batch\",\r\n            path: \"encollect/payments/v1/print-batch-list\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchBatch, ACMP.CanPrintBatch],\r\n          },\r\n          {\r\n            name: \"Search and Edit Batch\",\r\n            path: \"encollect/payments/v1/search-and-edit-batch\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchBatch, ACMP.CanUpdateBatch],\r\n          },\r\n          {\r\n            name: \"Receive Batch of Payments at Branch\",\r\n            path: \"encollect/payments/v1/receive-batch-of-payments\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanAcknowledgeBatch],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Deposit Slip\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Deposit Slip\",\r\n            path: \"encollect/payments/v1/create-pay-slip\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n          {\r\n            name: \"Search and View Deposit Slip\",\r\n            path: \"encollect/payments/v1/search-and-view-pay-in-slip\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSearchPIS],\r\n          },\r\n          {\r\n            name: \"Acknowledge Deposit Slip\",\r\n            path: \"encollect/payments/v1/central_ops_acknowledging\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanAcknowledgePIS],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Receipts\",\r\n        subMenus: [\r\n          {\r\n            name: \"Issue Receipt to Walk-in Customer\",\r\n            path: \"encollect/payments/v1/Walkin-customer-receipt\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateWalkinReceipt],\r\n          },\r\n          {\r\n            name: \"Send Duplicate Receipt\",\r\n            path: \"encollect/payments/v1/search-and-send-duplicate-email-e-receipt-and-SMS\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanSendDuplicateReceipt],\r\n          },\r\n          {\r\n            name: \"Raise Receipt Cancellation Request\",\r\n            path: \"encollect/payments/v1/reciept-cancellation-request\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [ACMP.CanCreateReceiptCancellationRequest],\r\n          },\r\n          {\r\n            name: \"Action Receipt Cancellation Request\",\r\n            path: \"encollect/payments/v1/reciept-cancellation-request-approval-reject\",\r\n            icon: \"fa fa-credit-card\",\r\n            acm: [\r\n              ACMP.CanApproveReceiptCancellationRequest,\r\n              ACMP.CanRejectReceiptCancellationRequest,\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Bulk Payments Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Payments Upload\",\r\n            path: \"encollect/payments/v1/bulk-payments\",\r\n            icon: \"fa fa-upload\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n          {\r\n            name: \"Bulk Payments Upload Status\",\r\n            path: \"encollect/payments/v1/bulk-payments-upload-status\",\r\n            icon: \"fa fa-upload\",\r\n            acm: [ACMP.CanCreatePIS],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Download Payment Report\",\r\n        path: \"encollect/payments/v1/download-payment-report\",\r\n        iconFa: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanDownloadPaymentReport],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Geo Report\",\r\n    icon: \"nav_geo_report_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"User Travel Report\",\r\n        path: \"travel-report/travel-report\",\r\n        icon: \"fa fa-users\",\r\n        acm: [ACMP.CanSearchTravelReport],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Digital ID\",\r\n    icon: \"nav_digital_id_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Digital ID Card\",\r\n        path: \"digital/card\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewDigitalIDCard],\r\n      },\r\n    ],\r\n  },\r\n   {\r\n    name: \"Settlement\",\r\n    icon: \"nav_settlement_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Find Eligible Cases\",\r\n        path: \"settlement/eligible-cases\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanFlagSettlementAsEligible],//CanViewDigitalIDCard\r\n      },\r\n       {\r\n        name: \"Request Settlement\",\r\n        path: \"settlement/request-settlement\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanRequestSettlement],\r\n      },\r\n       {\r\n        name: \"My Requests\",\r\n        path: \"settlement/my-settlement-summary\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewMySettlement],\r\n      },\r\n       {\r\n        name: \"My Action Queue\",\r\n        path: \"settlement/my-settlement-queue-summary\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [ACMP.CanViewMyQueueSettlement],\r\n      }\r\n    ],\r\n  },\r\n  {\r\n    name: \"Target Setting\",\r\n    icon: \"nav_target_setting_icon\",\r\n    hide: true,\r\n    subMenus: [\r\n      {\r\n        name: \"Upload Targets\",\r\n        path: \"target/upload-budgeted-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Upload Budgeted Target Status\",\r\n        path: \"target/budgeted-target-file-upload-status\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"View Budgeted Targets\",\r\n        path: \"target/view-budgeted-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Create Target\",\r\n        path: \"target/create-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Target Listing\",\r\n        path: \"target/search-target\",\r\n        icon: \"fa fa-credit-card\",\r\n        acm: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Curing Tools\",\r\n    icon: \"nav_curing_tools_icon\",\r\n    hide: true,\r\n    subMenus: [\r\n      {\r\n        name: \"Request Settlement\",\r\n        path: \"settlement/acs-request-settlement\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Requests\",\r\n        path: \"settlement/acs-mysettlement\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Settlement Queue\",\r\n        path: \"settlement/acs-settlement-queue\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Request Cure\",\r\n        path: \"encollect/cure/request-cure\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Cure\",\r\n        path: \"encollect/cure/my-cure\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Cure Queue\",\r\n        path: \"encollect/cure/my-cure-queue\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Add/Initiate Legal Case\",\r\n        path: \"encollect/legal-custom/create-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Legal Case\",\r\n        path: \"encollect/legal-custom/my-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Legal Queue\",\r\n        path: \"encollect/legal-custom/myqueue-legal\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Bulk Upload of Hearing Date\",\r\n        path: \"encollect/legal-custom/bulkupload-hearing\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Status of Bulk Upload of Hearing Date\",\r\n        path: \"encollect/legal-custom/bulkupload-hearing-status\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Bulk Upload to Initiate Case\",\r\n        path: \"encollect/legal-custom/bulkupload-initiate\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Status of Bulk Upload Initiate Case\",\r\n        path: \"encollect/legal-custom/bulkupload-initiate-status\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"Initiate Legal Request\",\r\n        path: \"encollect/legal/initiate-legal-request\",\r\n        icon: \"fa fa-users\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"My Legal Queue\",\r\n        path: \"encollect/legal/my-legal-queue\",\r\n        icon: \"fa fa-users\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Create Repossession\",\r\n        path: \"encollect/repossession/create-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Repossession\",\r\n        path: \"encollect/repossession/my-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n      {\r\n        name: \"My Queue Repossession\",\r\n        path: \"encollect/repossession/myqueue-repossession\",\r\n        icon: \"fa fa-users\",\r\n        acm: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Segmentation & Treatment\",\r\n    icon: \"nav_segmentation_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Segmentation\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Segment\",\r\n            path: \"segmentation/create-segmentation\",\r\n            acm: [ACMP.CanCreateSegment],\r\n          },\r\n          {\r\n            name: \"Search Segments\",\r\n            path: \"segmentation/search-segmentation\",\r\n            acm: [ACMP.CanSearchSegment],\r\n          },\r\n          {\r\n            name: \"Sequence Segments\",\r\n            path: \"segmentation/segmentation-sequence\",\r\n            acm: [ACMP.CanSequenceSegment],\r\n          },\r\n          {\r\n            name: \"Compare Segments\",\r\n            path: \"segmentation/compare-segmentation\",\r\n            acm: [ACMP.CanCompareSegment],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Treatment\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Treatment\",\r\n            path: \"treatment/create-treatment-step1\",\r\n            acm: [ACMP.CanCreateTreatment],\r\n          },\r\n          {\r\n            name: \"Search Treatments\",\r\n            path: \"treatment/search-treatment\",\r\n            acm: [ACMP.CanSearchTreatment],\r\n          },\r\n          {\r\n            name: \"Sequence Treatments\",\r\n            path: \"treatment/treatment-sequence\",\r\n            acm: [ACMP.CanSequenceTreatement],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Clear Segment / Treatment\",\r\n        path: \"segmentation/clear\",\r\n        acm: [ACMP.CanClearSegmentAndTreatementStamping],\r\n      },\r\n      {\r\n        name: \"Customer Search\",\r\n        path: \"coming-soon\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Account Search\",\r\n        path: \"coming-soon\",\r\n        hide: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"System Settings\",\r\n    icon: \"nav_system_settings_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Basic UI Settings\",\r\n        path: \"settings/main-settings\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Disposition Code Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Disposition Code Group\",\r\n            path: \"settings/disposition-group-config\",\r\n          },\r\n          {\r\n            name: \"Create Disposition Code\",\r\n            path: \"settings/disposition-code-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Deposit Bank Account Number Config\",\r\n        path: \"settings/disposition-account-number\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Allocations\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Allocation Configuration\",\r\n            path: \"settings/allocation-config\",\r\n          },\r\n          {\r\n            name: \"Bucket Configuration\",\r\n            path: \"settings/allocation-bucket-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Payments\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Offline Receipts Configuration\",\r\n            path: \"settings/payments/offline-receipt\",\r\n          },\r\n          {\r\n            name: \"Transaction Series Configuration\",\r\n            path: \"settings/payments/transaction-series\",\r\n          },\r\n          {\r\n            name: \"Mode of Payments Configuration\",\r\n            path: \"settings/payments/mode-of-payments\",\r\n          },\r\n          {\r\n            name: \"Issue Receipt Masters Configuration\",\r\n            path: \"settings/payments/denominations\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Geography Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Geography Master\",\r\n            path: \"settings/upload-geography-master\",\r\n          },\r\n          {\r\n            name: \"Upload Geography Master Status\",\r\n            path: \"settings/upload-geography-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Geography Master\",\r\n            path: \"settings/geography-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Area Code Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Area Code Master\",\r\n            path: \"settings/upload-area-code-master\",\r\n          },\r\n          {\r\n            name: \"Upload Area Code Master Status\",\r\n            path: \"settings/upload-area-code-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Area Code Master\",\r\n            path: \"settings/area-code-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Bank Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Bank Master\",\r\n            path: \"settings/upload-bank-master\",\r\n          },\r\n          {\r\n            name: \"Upload Bank Master Status\",\r\n            path: \"settings/upload-bank-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Bank Master\",\r\n            path: \"settings/bank-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Account Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Account Upload\",\r\n            path: \"settings/upload-account-import-master\",\r\n            acm: [ACMP.CanUploadBulkAccounts],\r\n          },\r\n          {\r\n            name: \"Account Upload Status\",\r\n            path: \"settings/upload-account-import-master-status\",\r\n            acm: [ACMP.CanSearchBulkAccountsUploadStatus],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Masters Upload\",\r\n        subMenus: [\r\n          {\r\n            name: \"Bulk Upload Masters\",\r\n            path: \"settings/bulk-upload-master\",\r\n            acm: [ACMP.CanUploadMasters],\r\n          },\r\n          {\r\n            name: \"Masters Upload Status\",\r\n            path: \"settings/bulk-upload-master-status\",\r\n            acm: [ACMP.CanSearchUploadMastersStatus],\r\n          },\r\n          {\r\n            name: \"View and Disable Masters\",\r\n            path: \"settings/view-masters\",\r\n            acm: [ACMP.CanSearchMasters], //SSViewDisableMastersScreen\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Define ACM\",\r\n        subMenus: [\r\n          {\r\n            name: \"Web\",\r\n            path: \"settings/define-web-acm\",\r\n            acm: [ACMP.CanDefineACM],\r\n          },\r\n          {\r\n            name: \"Mobile\",\r\n            path: \"settings/define-mobile-acm\",\r\n            acm: [ACMP.CanDefineACM],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Permissions\",\r\n        subMenus: [\r\n          {\r\n            name: \"Define Permission Schemes\",\r\n            path: \"settings/permissions/define-permission-group\",\r\n            acm: [ACMP.CanCreatePermissionScheme],\r\n          },\r\n          {\r\n            name: \"Search Permission Schemes\",\r\n            path: \"settings/permissions/search-permission-groups\",\r\n            acm: [ACMP.CanViewPermissionSchemes],\r\n          },\r\n          {\r\n            name: \"Assign Permission Scheme to Designations\",\r\n            path: \"settings/permissions/assign-designations-to-permission-groups\",\r\n            acm: [ACMP.CanViewDesignationSchemeDetails],\r\n          },\r\n           {\r\n            name: \"Search Permissions\",\r\n            path: \"settings/permissions/search-permissions\",\r\n            acm: [ACMP.CanSearchPermissions],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Account Detail Label Customization\",\r\n        path: \"settings/account-detail-label-customization\",\r\n        roles: [],\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Base Branch Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Upload Base Branch Master\",\r\n            path: \"settings/upload-branch-master\",\r\n          },\r\n          {\r\n            name: \"Upload Base Branch Master Status\",\r\n            path: \"settings/upload-branch-master-status\",\r\n          },\r\n          {\r\n            name: \"Search Base Branch Master\",\r\n            path: \"settings/branch-master-search\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Workflow\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Workflow\",\r\n            path: \"/workflows\",\r\n          },\r\n          {\r\n            name: \"Edit Workflow\",\r\n            path: \"/workflows/edit-workflow\",\r\n          },\r\n          {\r\n            name: \"View Workflow\",\r\n            path: \"/workflows/view-workflow\",\r\n          },\r\n          {\r\n            name: \"Search Workflow\",\r\n            path: \"/workflows/search-workflow\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Action\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Action\",\r\n            path: \"/action/create-action\",\r\n          },\r\n          {\r\n            name: \"Edit Action\",\r\n            path: \"/action/edit-action\",\r\n          },\r\n          {\r\n            name: \"View Action\",\r\n            path: \"/action/view-action\",\r\n          },\r\n          {\r\n            name: \"Search Action\",\r\n            path: \"/action/search-action\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Value\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Value\",\r\n            path: \"/value/create-value\",\r\n          },\r\n          {\r\n            name: \"Edit Value\",\r\n            path: \"/value/edit-value\",\r\n          },\r\n          {\r\n            name: \"View Value\",\r\n            path: \"/value/view-value\",\r\n          },\r\n          {\r\n            name: \"Search Value\",\r\n            path: \"/value/search-value\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Product Master\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Configure Product Group\",\r\n            path: \"settings/product-group-config\",\r\n          },\r\n          {\r\n            name: \"Configure Product\",\r\n            path: \"settings/product-config\",\r\n          },\r\n          {\r\n            name: \"Configure Sub Product\",\r\n            path: \"settings/sub-product-config\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Account Search Scope\",\r\n        path: \"settings/account-search-scope\",\r\n        roles: [\"SYSTEMADMIN\", \"BankToBackEndInternalBIHP\"],\r\n        // acm: [ACM.SSAccountSearchScopeScreen],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Communication\",\r\n    icon: \"nav_communication_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Search Communication Templates\",\r\n        path: \"communication/search-communication-templates\",\r\n        hasAccess: true,\r\n      },\r\n      {\r\n        name: \"Create Communication Template\",\r\n        path: \"communication/create-communication-template\",\r\n        hasAccess: true,\r\n      },\r\n      {\r\n        name: \"Search Communication Triggers\",\r\n        path: \"communication/search-communication-triggers\",\r\n        hasAccess: true,\r\n      },\r\n      {\r\n        name: \"Create Communication Trigger\",\r\n        path: \"communication/create-communication-trigger\",\r\n        hasAccess: true,\r\n      },\r\n      {\r\n        name: \"Communication Template\",\r\n        subMenus: [\r\n          {\r\n            name: \"Create Communication Template\",\r\n            path: \"communication/create-template\",\r\n            // acm: [ACMP.CanCreateCommunicationTemplate],\r\n          },\r\n          {\r\n            name: \"Search Communication Template\",\r\n            path: \"communication/search-template\",\r\n            acm: [ACMP.CanSearchCommunicationTemplate],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Communication Task\",\r\n        hide: true,\r\n        subMenus: [\r\n          {\r\n            name: \"Create Communication Task\",\r\n            path: \"communication/create-task\",\r\n            acm: [],\r\n          },\r\n          {\r\n            name: \"Search Communication Task\",\r\n            path: \"communication/search-task\",\r\n            acm: [],\r\n          },\r\n        ],\r\n      },\r\n      // {\r\n      //   name: \"Upload Letter\",\r\n      //   subMenus: [\r\n      //     {\r\n      //       name: \"Upload Letter Status\",\r\n      //       path: \"communication/Upload-letter-status\",\r\n      //       acm: [],\r\n      //     },\r\n      //     {\r\n      //       name: \"View Upload Letter File Status\",\r\n      //       path: \"communication/view-Upload-letter-status\",\r\n      //       acm: [],\r\n      //     },\r\n      //   ],\r\n      // },\r\n      // {\r\n      //   name: \"Broadcast\",\r\n      //   path: \"communication/broadcast\",\r\n      //   acm: [],\r\n      // },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Reports\",\r\n    icon: \"nav_reports_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Allocation Reports\",\r\n        subMenus: [\r\n          {\r\n            name: \"Agency Allocation Gap Report\",\r\n            path: \"reports/agency-gap-mis-report\",\r\n            acm: [ACMP.CanViewAgencyAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Agent Allocation Gap Report\",\r\n            path: \"reports/agent-gap-mis-report\",\r\n            acm: [ACMP.CanViewAgentAllocationGapReport],\r\n          },\r\n          {\r\n            name: \"Allocated vs Achieved Report\",\r\n            path: \"reports/allocated-vs-achieved-report\",\r\n            acm: [ACMP.CanViewAllocatedvsArchievedReport],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Trail Reports\",\r\n        subMenus: [\r\n          {\r\n            name: \"Trail Gap Report\",\r\n            path: \"reports/trail-gap-report\",\r\n            acm: [ACMP.CanViewTrailGapReport],\r\n          },\r\n          {\r\n            name: \"Trail History Report\",\r\n            path: \"reports/trail-history-report\",\r\n            acm: [ACMP.CanViewTrailHistoryReport],\r\n          },\r\n          {\r\n            name: \"Trail Intensity Report\",\r\n            path: \"reports/trail-intensity-report\",\r\n            acm: [ACMP.CanViewTrailIntensityReport],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        name: \"Payment Report\",\r\n        path: \"reports/payment-report\",\r\n        acm: [ACMP.CanViewPaymentReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Report\",\r\n        path: \"reports/money-movement-report\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n      {\r\n        name: \"Attendance Report\",\r\n        path: \"attendance/attendance-report\",\r\n        acm: [ACMP.CanViewAttendanceReport],\r\n      },\r\n      {\r\n        name: \"Communication History Report\",\r\n        path: \"reports/communication-history-report\",\r\n        acm: [ACMP.CanViewCommunicationHistoryReport],\r\n      },\r\n      {\r\n        name: \"Account Dashboard Report\",\r\n        path: \"reports/account-dashboard-report\",\r\n        acm: [ACMP.CanViewAccountDashboardReport],\r\n      },\r\n      {\r\n        name: \"Performance Report\",\r\n        path: \"reports/performance-report\",\r\n        acm: [ACMP.CanViewPerformanceReport],\r\n      },\r\n      {\r\n        name: \"Supervisory Report\",\r\n        path: \"reports/supervisory-report\",\r\n        acm: [ACMP.CanViewSupervisoryReport],\r\n      },\r\n      {\r\n        name: \"Collection Intensity Report\",\r\n        path: \"reports/collection-intensity-report\",\r\n        acm: [ACMP.CanViewCollectionIntensityReport],\r\n      },\r\n      {\r\n        name: \"Collection Trend Report\",\r\n        path: \"reports/collection-trend-report\",\r\n        acm: [ACMP.CanViewCollectionTrendReport],\r\n      },\r\n      {\r\n        name: \"Visit Intensity Report\",\r\n        path: \"reports/visit-intensity-report\",\r\n        acm: [ACMP.CanViewVisitIntensityReport],\r\n      },\r\n      {\r\n        name: \"Collection Dashboard\",\r\n        path: \"reports/collection-dashboard\",\r\n        hide: false,\r\n      },\r\n      {\r\n        name: \"Allocation vs Collections vs Trails Analysis Report\",\r\n        path: \"reports/allocation-collection-trails-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Target vs Actual Report\",\r\n        path: \"reports/target-actual-analysis-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Daily Legal Report\",\r\n        path: \"reports/daily-legal-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Daily Repossession Report\",\r\n        path: \"reports/daily-repo-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Allocation vs Collections vs Trails Analysis Report Sample\",\r\n        path: \"demo-reports/allocation-collection-trails-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Supervisory Report Sample\",\r\n        path: \"demo-reports/supervisory-report\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Collection Dashboard Sample\",\r\n        path: \"demo-reports/collection-dashboard\",\r\n        hide: true,\r\n      },\r\n      {\r\n        name: \"Customer Contact Report\",\r\n        path: \"reports/ccd-report\",\r\n        acm: [ACMP.CanViewCustomerContactReport],\r\n      },\r\n      {\r\n        name: \"Cash Wallet Limit Report\",\r\n        path: \"reports/cash-wallet-limit-report\",\r\n        acm: [ACMP.CanViewCashWalletLimitReport],//CanViewCashWalletLimitReport\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Insights\",\r\n    icon: \"nav_insights_icon\",\r\n    subMenus: [\r\n      {\r\n        name: \"Primary Allocation Insights\",\r\n        path: \"insights/primary-allocation-insights\",\r\n        acm: [ACMP.CanViewAgencyAllocationGapReport],\r\n      },\r\n      {\r\n        name: \"Secondary Allocation Insights\",\r\n        path: \"insights/secondary-allocation-insights\",\r\n        acm: [ACMP.CanViewAgentAllocationGapReport],\r\n      },\r\n      {\r\n        name: \"Trail Gap Insights\",\r\n        path: \"insights/trail-gap-insights\",\r\n        acm: [ACMP.CanViewTrailGapReport],\r\n      },\r\n      {\r\n        name: \"Allocated vs Achieved Insights\",\r\n        path: \"insights/allocated-vs-achieved-insights\",\r\n        acm: [ACMP.CanViewAllocatedvsArchievedReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Insights (Agency Staff)\",\r\n        path: \"insights/money-movement-insights-agency-staff\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n      {\r\n        name: \"Money Movement Insights (Bank Staff)\",\r\n        path: \"insights/money-movement-insights-bank-staff\",\r\n        acm: [ACMP.CanViewMoneyMovementReport],\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAmB,eAAe;AAC5D,SACEC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,MAAM,QACD,iBAAiB;AACxB,SAASC,QAAQ,EAAEC,GAAG,QAAoB,MAAM;AAChD,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,IAAI,EAAEC,UAAU,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,kCAAkC;AAGlE,MAAMC,YAAY,GAAiBC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAiB,IAAI,MAAM;AAO1F,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAmB9BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,UAAsB,EACtBC,MAAqB;IAHrB,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IAtBR,KAAAC,eAAe,GAAGtB,MAAM,CAACY,eAAe,CAAC;IAE1C,KAAAW,YAAY,GAAGjB,QAAQ,CAAC,IAAI,CAAC,CAACkB,IAAI,CAACjB,GAAG,CAAC,MAAM,IAAIkB,IAAI,EAAE,CAAC,CAAC;IAIzD,KAAAC,SAAS,GAAY,KAAK;IAI1B,KAAAC,iBAAiB,GAAYd,YAAY,KAAK,MAAM;IACpD,KAAAA,YAAY,GAAiBA,YAAY;IACzC,KAAAe,YAAY,GAED,4xDAA4xD,CAAC,CAAC;IACzyD,KAAAC,OAAO,GAAyB,EAAE;IAEzC,KAAAC,WAAW,GAAG,EAAE;IAOd,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACZ,WAAW,CAACa,eAAe;IACxD,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACF,gBAAgB,CAACG,SAAS,CAAEC,SAAkB,IAAI;MACrD,IAAIA,SAAS,EAAE;QACb,IAAI,CAACC,YAAY,EAAE;MACrB;MACA,IAAI,CAACN,WAAW,GAAG,IAAI,CAACR,eAAe,CAACe,cAAc,EAAE;IAC1D,CAAC,CAAC;IACF,IAAI,CAACnB,MAAM,CAACoB,MAAM,CAACJ,SAAS,CAAEK,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAYnC,eAAe,EAAE;QACpC,IAAI,CAACsB,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IACLa,KAAK,YAAYrC,aAAa,IAC9BqC,KAAK,YAAYtC,gBAAgB,IACjCsC,KAAK,YAAYpC,eAAe,EAChC;QACA,IAAI,CAACuB,SAAS,GAAG,KAAK;MACxB;IACF,CAAC,CAAC;EACJ;EAEQO,gBAAgBA,CAAA;IACtB,IAAI,CAACd,WAAW,CAACqB,WAAW,CAACN,SAAS,CAAEO,QAAQ,IAAS;MACvD,IAAI,CAACD,WAAW,GAAGC,QAAQ;MAC3B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,WAAW,EAAEG,KAAK,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,EAAEC,oBAAoB,CAAC,IAAI,IAAI,CAACN,WAAW,EAAEG,KAAK,GAAG,CAAC,CAAC;MAC9G,IAAI,CAACI,gBAAgB,CAACN,QAAQ,EAAEb,YAAY,CAAC;MAC7C,IAAI,CAACoB,YAAY,CAACP,QAAQ,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAQ,aAAaA,CAAA;IACX,IAAI,CAACpC,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;IAClE,IAAI,CAACc,iBAAiB,GAAG,IAAI,CAACd,YAAY,KAAK,MAAM;IACrDC,YAAY,CAACoC,OAAO,CAAC,cAAc,EAAE,GAAG,IAAI,CAACrC,YAAY,EAAE,CAAC;EAC9D;EAEAsC,YAAYA,CAACC,KAAc;IACzB,IAAI,IAAI,CAACvC,YAAY,KAAK,MAAM,EAAE;MAChC,IAAI,CAACc,iBAAiB,GAAGyB,KAAK;IAChC;EACF;EAEAC,gBAAgBA,CAACC,IAAS,EAAEC,KAAY;IACtC,IAAID,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAE;MAC1BF,KAAK,CAACG,OAAO,CAAEb,CAAC,IAAI;QAClB,IAAIS,IAAI,CAACK,IAAI,KAAKd,CAAC,CAACc,IAAI,EAAEd,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK;MACjD,CAAC,CAAC;MACFS,IAAI,CAAC,UAAU,CAAC,GAAG,CAACA,IAAI,EAAEM,QAAQ;IACpC,CAAC,MAAM,IAAIN,IAAI,EAAEO,IAAI,EAAE;MACrB,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAACR,IAAI,EAAEO,IAAI,CAAC,CAAC;IACpC;EACF;EAEAzB,YAAYA,CAAA;IACV,MAAM2B,SAAS,GAAIC,OAAkB,IACnCA,OAAO,CAACC,MAAM,CAAC,CAACC,IAAe,EAAEZ,IAAa,KAAI;MAChD,IAAIA,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAE;QAC1B,MAAMU,QAAQ,GAAGJ,SAAS,CAACT,IAAI,EAAEE,QAAQ,CAAC;QAC1CF,IAAI,CAACE,QAAQ,GAAGW,QAAQ;QACxBb,IAAI,CAACc,SAAS,GAAGD,QAAQ,CAACE,IAAI,CAAEf,IAAa,IAAKA,IAAI,CAACc,SAAS,CAAC;QACjEd,IAAI,CAACgB,KAAK,GAAGhB,IAAI,EAAEK,IAAI,EACnBY,WAAW,EAAE,EACbC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EACpBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACxB,CAAC,MAAM,IAAIlB,IAAI,EAAEmB,GAAG,EAAEhB,MAAM,EAAE;QAC5BH,IAAI,CAACc,SAAS,GAAG,IAAI,CAAChD,UAAU,CAACsD,YAAY,CAACpB,IAAI,EAAEmB,GAAG,CAAC;MAC1D,CAAC,MAAM,IAAInB,IAAI,EAAEX,KAAK,EAAEc,MAAM,EAAE;QAC9BH,IAAI,CAACc,SAAS,GAAG,IAAI,CAAChD,UAAU,CAACuD,aAAa,CAACrB,IAAI,EAAEX,KAAK,CAAC;MAC7D,CAAC,MAAM;QACLW,IAAI,CAACc,SAAS,GAAGd,IAAI,EAAEc,SAAS,IAAI,KAAK;MAC3C;MACA,OAAOd,IAAI,EAAEmB,GAAG;MAChBP,IAAI,CAACU,IAAI,CAACtB,IAAI,CAAC;MACf,OAAOY,IAAI;IACb,CAAC,EAAE,EAAE,CAAC;IACR,MAAMF,OAAO,GAAGD,SAAS,CAACc,SAAS,EAAE,CAAC;IACtC,IAAI,CAACb,OAAO,GAAGA,OAAO;EACxB;EAEAc,WAAWA,CAAA,GAAU;EAEb/B,gBAAgBA,CAACnB,YAAoB;IAC3C,IAAIA,YAAY,EAAE;MAChB,IAAI,CAACT,WAAW,CAAC4D,WAAW,CAACnD,YAAY,CAAC,CAACM,SAAS,CAAE8C,GAAG,IAAI;QAC3D,MAAMC,SAAS,GAAG,YAAY;QAC9B,MAAMC,eAAe,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;QAC3C,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,eAAe,CAAC,EAAE;UAAEI,IAAI,EAAEL;QAAS,CAAE,CAAC;QAC7D,MAAMM,QAAQ,GAAe,IAAIC,UAAU,EAAE;QAC7CD,QAAQ,CAACE,SAAS,GAAIC,CAAC,IAAI;UACzB,IAAI,CAAC9D,YAAY,GAAG2D,QAAQ,CAACI,MAAM;QACrC,CAAC;QACDJ,QAAQ,CAACK,aAAa,CAACR,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ;EACF;EAEQpC,YAAYA,CAAC6C,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAEC,EAAE,EAAE;IACf,IAAI,CAAC3E,WAAW,CAAC4E,OAAO,EAAE,CAAC7D,SAAS,CACjC8C,GAAG,IAAI;MACN,MAAMC,SAAS,GAAG,YAAY;MAC9B,MAAMC,eAAe,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;MAC3C,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,eAAe,CAAC,EAAE;QAAEI,IAAI,EAAEL;MAAS,CAAE,CAAC;MAC7D,MAAMM,QAAQ,GAAe,IAAIC,UAAU,EAAE;MAC7CD,QAAQ,CAACE,SAAS,GAAIC,CAAC,IAAI;QACzB,IAAI,CAAC7D,OAAO,GAAG0D,QAAQ,CAACI,MAAM;MAChC,CAAC;MACDJ,QAAQ,CAACK,aAAa,CAACR,IAAI,CAAC;IAC9B,CAAC,EACAY,GAAG,IAAI;MACN,IAAI,CAAC3E,MAAM,CAAC4E,KAAK,CAAC,mBAAmB,CAAC;IACxC,CAAC,CACF;EACH;;;;;;;;;;;;;AA1IWjF,mBAAmB,GAAAkF,UAAA,EAL/BnG,SAAS,CAAC;EACToG,QAAQ,EAAE,iBAAiB;EAC3BC,QAAA,EAAAC,oBAA2C;;CAE5C,CAAC,C,EACWrF,mBAAmB,CA2I/B;;AAeD,MAAM6D,SAAS,GAAGA,CAAA,KAAM,CACtB;EACElB,IAAI,EAAE,gBAAgB;EACtB2C,IAAI,EAAE,eAAe;EACrBzC,IAAI,EAAE,yBAAyB;EAC/B;EACAY,GAAG,EAAE,CAAChE,IAAI,CAAC8F,iBAAiB;CAC7B,EACD;EACE5C,IAAI,EAAE,iBAAiB;EACvB2C,IAAI,EAAE,oBAAoB;EAC1B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,YAAY;MAClBE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAAChE,IAAI,CAAC+F,eAAe;KAC3B,EACD;MACE7C,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAAChE,IAAI,CAACgG,eAAe;KAC3B;GAEJ,EACD;IACE9C,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,WAAW;MACjBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAAChE,IAAI,CAACiG,cAAc;KAC1B,EACD;MACE/C,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAAChE,IAAI,CAACkG,cAAc;KAC1B;GAEJ,EACD;IACEhD,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,WAAW;MACjBE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAAChE,IAAI,CAACmG,cAAc;KAC1B,EACD;MACEjD,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAAChE,IAAI,CAACoG,cAAc;KAC1B;GAEJ,EACD;IACElD,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,qCAAqC;IAC3CiD,IAAI,EAAE;IACN;GACD,EACD;IACEnD,IAAI,EAAE,aAAa;IACnBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,sCAAsC;MAC5CY,GAAG,EAAE,CAAChE,IAAI,CAACsG,iBAAiB;KAC7B,EACD;MACEpD,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,6CAA6C;MACnDY,GAAG,EAAE,CAAChE,IAAI,CAACuG,6BAA6B;KACzC,EACD;MACErD,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,gDAAgD;MACtDY,GAAG,EAAE,CAAChE,IAAI,CAACwG,8BAA8B;KAC1C,EACD;MACEtD,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE,uDAAuD;MAC7DY,GAAG,EAAE,CAAChE,IAAI,CAACyG,oCAAoC;KAChD;GAEJ;CAEJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvD,IAAI,EAAE,YAAY;EAClB2C,IAAI,EAAE,qBAAqB;EAC3B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sCAAsC;MAC5CE,IAAI,EAAE,wDAAwD;MAC9DY,GAAG,EAAE,CAAChE,IAAI,CAAC0G,+BAA+B;KAC3C,EACD;MACExD,IAAI,EAAE,uCAAuC;MAC7CE,IAAI,EAAE,sEAAsE;MAC5EY,GAAG,EAAE,CAAChE,IAAI,CAAC0G,+BAA+B;KAC3C,EACD;MACExD,IAAI,EAAE,wCAAwC;MAC9CE,IAAI,EAAE,0DAA0D;MAChEY,GAAG,EAAE,CAAChE,IAAI,CAAC2G,iCAAiC;KAC7C,EACD;MACEzD,IAAI,EAAE,yCAAyC;MAC/CE,IAAI,EAAE,wEAAwE;MAC9EY,GAAG,EAAE,CAAChE,IAAI,CAAC2G,iCAAiC;KAC7C,EACD;MACEzD,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,mDAAmD;MACzDY,GAAG,EAAE,CAAChE,IAAI,CAAC4G,qCAAqC;KACjD,EACD;MACE1D,IAAI,EAAE,4BAA4B;MAClCE,IAAI,EAAE,qDAAqD;MAC3DY,GAAG,EAAE,CAAChE,IAAI,CAAC6G,uCAAuC;KACnD;GAEJ,EACD;IACE3D,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,2DAA2D;MACjEyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAAC8G,iCAAiC;KAC7C,EACD;MACE5D,IAAI,EAAE,sCAAsC;MAC5CE,IAAI,EAAE,yEAAyE;MAC/EyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAAC8G,iCAAiC;KAC7C,EACD;MACE5D,IAAI,EAAE,uCAAuC;MAC7CE,IAAI,EAAE,6DAA6D;MACnEyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAAC+G,mCAAmC;KAC/C,EACD;MACE7D,IAAI,EAAE,wCAAwC;MAC9CE,IAAI,EAAE,2EAA2E;MACjFyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAAC+G,mCAAmC;KAC/C,EACD;MACE7D,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE,qDAAqD;MAC3DyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACgH,uCAAuC;KACnD,EACD;MACE9D,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,uDAAuD;MAC7DyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACiH,yCAAyC;KACrD;GAEJ,EACD;IACE/D,IAAI,EAAE,8BAA8B;IACpCH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,4CAA4C;MAClDE,IAAI,EAAE,wDAAwD;MAC9DyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACkH,6BAA6B;KACzC,EACD;MACEhE,IAAI,EAAE,6CAA6C;MACnDE,IAAI,EAAE,sEAAsE;MAC5EyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACkH,6BAA6B;KACzC,EACD;MACEhE,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE,+DAA+D;MACrEyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACmH,mCAAmC;KAC/C;GAEJ,EACD;IACEjE,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,oDAAoD;MAC1DyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACoH,kCAAkC;KAC9C,EACD;MACElE,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,sDAAsD;MAC5DyC,IAAI,EAAE,iBAAiB;MACvB7B,GAAG,EAAE,CAAChE,IAAI,CAACqH,oCAAoC;KAChD;GAEJ;CAEJ,EACD;EACEnE,IAAI,EAAE,QAAQ;EACd2C,IAAI,EAAE,iBAAiB;EACvB9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,oCAAoC;IAC1CyC,IAAI,EAAE,cAAc;IACpB7B,GAAG,EAAE,CAAChE,IAAI,CAACsH,kBAAkB;GAC9B,EACD;IACEpE,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,2CAA2C;IACjDyC,IAAI,EAAE,cAAc;IACpB7B,GAAG,EAAE,CAAChE,IAAI,CAACuH,wBAAwB;GACpC;CAEJ,EACD;EACErE,IAAI,EAAE,UAAU;EAChB2C,IAAI,EAAE,mBAAmB;EACzB9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,oDAAoD;IAC1DyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACwH,qBAAqB;GACjC,EACD;IACEtE,IAAI,EAAE,mBAAmB;IACzBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,gDAAgD;MACtDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACyH,cAAc;KAC1B,EACD;MACEvE,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,wCAAwC;MAC9CyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC0H,cAAc,EAAE1H,IAAI,CAAC2H,aAAa;KAC9C,EACD;MACEzE,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,6CAA6C;MACnDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC0H,cAAc,EAAE1H,IAAI,CAAC4H,cAAc;KAC/C,EACD;MACE1E,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,iDAAiD;MACvDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC6H,mBAAmB;KAC/B;GAEJ,EACD;IACE3E,IAAI,EAAE,cAAc;IACpBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,uCAAuC;MAC7CyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC8H,YAAY;KACxB,EACD;MACE5E,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,mDAAmD;MACzDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC+H,YAAY;KACxB,EACD;MACE7E,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,iDAAiD;MACvDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACgI,iBAAiB;KAC7B;GAEJ,EACD;IACE9E,IAAI,EAAE,UAAU;IAChBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,mCAAmC;MACzCE,IAAI,EAAE,+CAA+C;MACrDyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACiI,sBAAsB;KAClC,EACD;MACE/E,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,yEAAyE;MAC/EyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACkI,uBAAuB;KACnC,EACD;MACEhF,IAAI,EAAE,oCAAoC;MAC1CE,IAAI,EAAE,oDAAoD;MAC1DyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACmI,mCAAmC;KAC/C,EACD;MACEjF,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE,oEAAoE;MAC1EyC,IAAI,EAAE,mBAAmB;MACzB7B,GAAG,EAAE,CACHhE,IAAI,CAACoI,oCAAoC,EACzCpI,IAAI,CAACqI,mCAAmC;KAE3C;GAEJ,EACD;IACEnF,IAAI,EAAE,sBAAsB;IAC5BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,qCAAqC;MAC3CyC,IAAI,EAAE,cAAc;MACpB7B,GAAG,EAAE,CAAChE,IAAI,CAAC8H,YAAY;KACxB,EACD;MACE5E,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,mDAAmD;MACzDyC,IAAI,EAAE,cAAc;MACpB7B,GAAG,EAAE,CAAChE,IAAI,CAAC8H,YAAY;KACxB;GAEJ,EACD;IACE5E,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,+CAA+C;IACrDkF,MAAM,EAAE,mBAAmB;IAC3BtE,GAAG,EAAE,CAAChE,IAAI,CAACuI,wBAAwB;GACpC;CAEJ,EACD;EACErF,IAAI,EAAE,YAAY;EAClB2C,IAAI,EAAE,qBAAqB;EAC3B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,6BAA6B;IACnCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE,CAAChE,IAAI,CAACwI,qBAAqB;GACjC;CAEJ,EACD;EACEtF,IAAI,EAAE,YAAY;EAClB2C,IAAI,EAAE,qBAAqB;EAC3B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,cAAc;IACpByC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAACyI,oBAAoB;GAChC;CAEJ,EACA;EACCvF,IAAI,EAAE,YAAY;EAClB2C,IAAI,EAAE,qBAAqB;EAC3B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,2BAA2B;IACjCyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC0I,2BAA2B,CAAC,CAAC;GACzC,EACA;IACCxF,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,+BAA+B;IACrCyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC2I,oBAAoB;GAChC,EACA;IACCzF,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,kCAAkC;IACxCyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC4I,mBAAmB;GAC/B,EACA;IACC1F,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,wCAAwC;IAC9CyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE,CAAChE,IAAI,CAAC6I,wBAAwB;GACpC;CAEJ,EACD;EACE3F,IAAI,EAAE,gBAAgB;EACtB2C,IAAI,EAAE,yBAAyB;EAC/BQ,IAAI,EAAE,IAAI;EACVtD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,+BAA+B;IACrCyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,2CAA2C;IACjDyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,6BAA6B;IACnCyC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,sBAAsB;IAC5ByC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,sBAAsB;IAC5ByC,IAAI,EAAE,mBAAmB;IACzB7B,GAAG,EAAE;GACN;CAEJ,EACD;EACEd,IAAI,EAAE,cAAc;EACpB2C,IAAI,EAAE,uBAAuB;EAC7BQ,IAAI,EAAE,IAAI;EACVtD,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,mCAAmC;IACzCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,6BAA6B;IACnCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,iCAAiC;IACvCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,cAAc;IACpBE,IAAI,EAAE,6BAA6B;IACnCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,SAAS;IACfE,IAAI,EAAE,wBAAwB;IAC9ByC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,8BAA8B;IACpCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,qCAAqC;IAC3CyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE,iCAAiC;IACvCyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,sCAAsC;IAC5CyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,2CAA2C;IACjDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uCAAuC;IAC7CE,IAAI,EAAE,kDAAkD;IACxDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,4CAA4C;IAClDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,qCAAqC;IAC3CE,IAAI,EAAE,mDAAmD;IACzDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,wBAAwB;IAC9BE,IAAI,EAAE,wCAAwC;IAC9CyC,IAAI,EAAE,aAAa;IACnBQ,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,gCAAgC;IACtCyC,IAAI,EAAE,aAAa;IACnBQ,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,qBAAqB;IAC3BE,IAAI,EAAE,4CAA4C;IAClDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,wCAAwC;IAC9CyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN,EACD;IACEd,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,6CAA6C;IACnDyC,IAAI,EAAE,aAAa;IACnB7B,GAAG,EAAE;GACN;CAEJ,EACD;EACEd,IAAI,EAAE,0BAA0B;EAChC2C,IAAI,EAAE,uBAAuB;EAC7B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,cAAc;IACpBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,gBAAgB;MACtBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAAChE,IAAI,CAAC8I,gBAAgB;KAC5B,EACD;MACE5F,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAAChE,IAAI,CAAC+I,gBAAgB;KAC5B,EACD;MACE7F,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE,oCAAoC;MAC1CY,GAAG,EAAE,CAAChE,IAAI,CAACgJ,kBAAkB;KAC9B,EACD;MACE9F,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,mCAAmC;MACzCY,GAAG,EAAE,CAAChE,IAAI,CAACiJ,iBAAiB;KAC7B;GAEJ,EACD;IACE/F,IAAI,EAAE,WAAW;IACjBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,kCAAkC;MACxCY,GAAG,EAAE,CAAChE,IAAI,CAACkJ,kBAAkB;KAC9B,EACD;MACEhG,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAAChE,IAAI,CAACmJ,kBAAkB;KAC9B,EACD;MACEjG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAAChE,IAAI,CAACoJ,qBAAqB;KACjC;GAEJ,EACD;IACElG,IAAI,EAAE,2BAA2B;IACjCE,IAAI,EAAE,oBAAoB;IAC1BY,GAAG,EAAE,CAAChE,IAAI,CAACqJ,oCAAoC;GAChD,EACD;IACEnG,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,aAAa;IACnBiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,aAAa;IACnBiD,IAAI,EAAE;GACP;CAEJ,EACD;EACEnD,IAAI,EAAE,iBAAiB;EACvB2C,IAAI,EAAE,0BAA0B;EAChC9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,wBAAwB;IAC9BiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,yBAAyB;IAC/BmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,+BAA+B;MACrCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,oCAAoC;IAC1CE,IAAI,EAAE,qCAAqC;IAC3CiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,aAAa;IACnBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,UAAU;IAChBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,qCAAqC;MAC3CE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,gCAAgC;MACtCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,aAAa;IACnBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,gBAAgB;IACtBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,uCAAuC;MAC7CY,GAAG,EAAE,CAAChE,IAAI,CAACsJ,qBAAqB;KACjC,EACD;MACEpG,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,8CAA8C;MACpDY,GAAG,EAAE,CAAChE,IAAI,CAACuJ,iCAAiC;KAC7C;GAEJ,EACD;IACErG,IAAI,EAAE,gBAAgB;IACtBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,qBAAqB;MAC3BE,IAAI,EAAE,6BAA6B;MACnCY,GAAG,EAAE,CAAChE,IAAI,CAACwJ,gBAAgB;KAC5B,EACD;MACEtG,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE,oCAAoC;MAC1CY,GAAG,EAAE,CAAChE,IAAI,CAACyJ,4BAA4B;KACxC,EACD;MACEvG,IAAI,EAAE,0BAA0B;MAChCE,IAAI,EAAE,uBAAuB;MAC7BY,GAAG,EAAE,CAAChE,IAAI,CAAC0J,gBAAgB,CAAC,CAAE;KAC/B;GAEJ,EACD;IACExG,IAAI,EAAE,YAAY;IAClBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,KAAK;MACXE,IAAI,EAAE,yBAAyB;MAC/BY,GAAG,EAAE,CAAChE,IAAI,CAAC2J,YAAY;KACxB,EACD;MACEzG,IAAI,EAAE,QAAQ;MACdE,IAAI,EAAE,4BAA4B;MAClCY,GAAG,EAAE,CAAChE,IAAI,CAAC2J,YAAY;KACxB;GAEJ,EACD;IACEzG,IAAI,EAAE,aAAa;IACnBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,8CAA8C;MACpDY,GAAG,EAAE,CAAChE,IAAI,CAAC4J,yBAAyB;KACrC,EACD;MACE1G,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,+CAA+C;MACrDY,GAAG,EAAE,CAAChE,IAAI,CAAC6J,wBAAwB;KACpC,EACD;MACE3G,IAAI,EAAE,0CAA0C;MAChDE,IAAI,EAAE,+DAA+D;MACrEY,GAAG,EAAE,CAAChE,IAAI,CAAC8J,+BAA+B;KAC3C,EACA;MACC5G,IAAI,EAAE,oBAAoB;MAC1BE,IAAI,EAAE,yCAAyC;MAC/CY,GAAG,EAAE,CAAChE,IAAI,CAAC+J,oBAAoB;KAChC;GAEJ,EACD;IACE7G,IAAI,EAAE,oCAAoC;IAC1CE,IAAI,EAAE,6CAA6C;IACnDlB,KAAK,EAAE,EAAE;IACTmE,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,oBAAoB;IAC1BmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,kCAAkC;MACxCE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,UAAU;IAChBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,QAAQ;IACdmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,aAAa;MACnBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,aAAa;MACnBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,eAAe;MACrBE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,OAAO;IACbmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,YAAY;MAClBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,YAAY;MAClBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,cAAc;MACpBE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,gBAAgB;IACtBmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,yBAAyB;MAC/BE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE;KACP,EACD;MACEF,IAAI,EAAE,uBAAuB;MAC7BE,IAAI,EAAE;KACP;GAEJ,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BE,IAAI,EAAE,+BAA+B;IACrClB,KAAK,EAAE,CAAC,aAAa,EAAE,2BAA2B;IAClD;GACD;CAEJ,EACD;EACEgB,IAAI,EAAE,eAAe;EACrB2C,IAAI,EAAE,wBAAwB;EAC9B9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,gCAAgC;IACtCE,IAAI,EAAE,8CAA8C;IACpDO,SAAS,EAAE;GACZ,EACD;IACET,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,6CAA6C;IACnDO,SAAS,EAAE;GACZ,EACD;IACET,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,6CAA6C;IACnDO,SAAS,EAAE;GACZ,EACD;IACET,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,4CAA4C;IAClDO,SAAS,EAAE;GACZ,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,+BAA+B;MACrCE,IAAI,EAAE;MACN;KACD,EACD;MACEF,IAAI,EAAE,+BAA+B;MACrCE,IAAI,EAAE,+BAA+B;MACrCY,GAAG,EAAE,CAAChE,IAAI,CAACgK,8BAA8B;KAC1C;GAEJ,EACD;IACE9G,IAAI,EAAE,oBAAoB;IAC1BmD,IAAI,EAAE,IAAI;IACVtD,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,2BAA2B;MACjCY,GAAG,EAAE;KACN,EACD;MACEd,IAAI,EAAE,2BAA2B;MACjCE,IAAI,EAAE,2BAA2B;MACjCY,GAAG,EAAE;KACN;;EAGL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;CAEH,EACD;EACEd,IAAI,EAAE,SAAS;EACf2C,IAAI,EAAE,kBAAkB;EACxB9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,oBAAoB;IAC1BH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,+BAA+B;MACrCY,GAAG,EAAE,CAAChE,IAAI,CAACiK,gCAAgC;KAC5C,EACD;MACE/G,IAAI,EAAE,6BAA6B;MACnCE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAAChE,IAAI,CAACkK,+BAA+B;KAC3C,EACD;MACEhH,IAAI,EAAE,8BAA8B;MACpCE,IAAI,EAAE,sCAAsC;MAC5CY,GAAG,EAAE,CAAChE,IAAI,CAACmK,iCAAiC;KAC7C;GAEJ,EACD;IACEjH,IAAI,EAAE,eAAe;IACrBH,QAAQ,EAAE,CACR;MACEG,IAAI,EAAE,kBAAkB;MACxBE,IAAI,EAAE,0BAA0B;MAChCY,GAAG,EAAE,CAAChE,IAAI,CAACoK,qBAAqB;KACjC,EACD;MACElH,IAAI,EAAE,sBAAsB;MAC5BE,IAAI,EAAE,8BAA8B;MACpCY,GAAG,EAAE,CAAChE,IAAI,CAACqK,yBAAyB;KACrC,EACD;MACEnH,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE,gCAAgC;MACtCY,GAAG,EAAE,CAAChE,IAAI,CAACsK,2BAA2B;KACvC;GAEJ,EACD;IACEpH,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE,wBAAwB;IAC9BY,GAAG,EAAE,CAAChE,IAAI,CAACuK,oBAAoB;GAChC,EACD;IACErH,IAAI,EAAE,uBAAuB;IAC7BE,IAAI,EAAE,+BAA+B;IACrCY,GAAG,EAAE,CAAChE,IAAI,CAACwK,0BAA0B;GACtC,EACD;IACEtH,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,8BAA8B;IACpCY,GAAG,EAAE,CAAChE,IAAI,CAACyK,uBAAuB;GACnC,EACD;IACEvH,IAAI,EAAE,8BAA8B;IACpCE,IAAI,EAAE,sCAAsC;IAC5CY,GAAG,EAAE,CAAChE,IAAI,CAAC0K,iCAAiC;GAC7C,EACD;IACExH,IAAI,EAAE,0BAA0B;IAChCE,IAAI,EAAE,kCAAkC;IACxCY,GAAG,EAAE,CAAChE,IAAI,CAAC2K,6BAA6B;GACzC,EACD;IACEzH,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCY,GAAG,EAAE,CAAChE,IAAI,CAAC4K,wBAAwB;GACpC,EACD;IACE1H,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCY,GAAG,EAAE,CAAChE,IAAI,CAAC6K,wBAAwB;GACpC,EACD;IACE3H,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,qCAAqC;IAC3CY,GAAG,EAAE,CAAChE,IAAI,CAAC8K,gCAAgC;GAC5C,EACD;IACE5H,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,iCAAiC;IACvCY,GAAG,EAAE,CAAChE,IAAI,CAAC+K,4BAA4B;GACxC,EACD;IACE7H,IAAI,EAAE,wBAAwB;IAC9BE,IAAI,EAAE,gCAAgC;IACtCY,GAAG,EAAE,CAAChE,IAAI,CAACgL,2BAA2B;GACvC,EACD;IACE9H,IAAI,EAAE,sBAAsB;IAC5BE,IAAI,EAAE,8BAA8B;IACpCiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,qDAAqD;IAC3DE,IAAI,EAAE,6CAA6C;IACnDiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,uCAAuC;IAC7CiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,4BAA4B;IAClCiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,2BAA2B;IACjCE,IAAI,EAAE,2BAA2B;IACjCiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,4DAA4D;IAClEE,IAAI,EAAE,kDAAkD;IACxDiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,2BAA2B;IACjCE,IAAI,EAAE,iCAAiC;IACvCiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,mCAAmC;IACzCiD,IAAI,EAAE;GACP,EACD;IACEnD,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE,oBAAoB;IAC1BY,GAAG,EAAE,CAAChE,IAAI,CAACiL,4BAA4B;GACxC,EACD;IACE/H,IAAI,EAAE,0BAA0B;IAChCE,IAAI,EAAE,kCAAkC;IACxCY,GAAG,EAAE,CAAChE,IAAI,CAACkL,4BAA4B,CAAC,CAAC;GAC1C;CAEJ,EACD;EACEhI,IAAI,EAAE,UAAU;EAChB2C,IAAI,EAAE,mBAAmB;EACzB9C,QAAQ,EAAE,CACR;IACEG,IAAI,EAAE,6BAA6B;IACnCE,IAAI,EAAE,sCAAsC;IAC5CY,GAAG,EAAE,CAAChE,IAAI,CAACiK,gCAAgC;GAC5C,EACD;IACE/G,IAAI,EAAE,+BAA+B;IACrCE,IAAI,EAAE,wCAAwC;IAC9CY,GAAG,EAAE,CAAChE,IAAI,CAACkK,+BAA+B;GAC3C,EACD;IACEhH,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE,6BAA6B;IACnCY,GAAG,EAAE,CAAChE,IAAI,CAACoK,qBAAqB;GACjC,EACD;IACElH,IAAI,EAAE,gCAAgC;IACtCE,IAAI,EAAE,yCAAyC;IAC/CY,GAAG,EAAE,CAAChE,IAAI,CAACmK,iCAAiC;GAC7C,EACD;IACEjH,IAAI,EAAE,wCAAwC;IAC9CE,IAAI,EAAE,+CAA+C;IACrDY,GAAG,EAAE,CAAChE,IAAI,CAACwK,0BAA0B;GACtC,EACD;IACEtH,IAAI,EAAE,sCAAsC;IAC5CE,IAAI,EAAE,6CAA6C;IACnDY,GAAG,EAAE,CAAChE,IAAI,CAACwK,0BAA0B;GACtC;CAEJ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}