<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <h2 class="title">Search Communication Templates</h2>
  <div class="enc-card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h3>Manage your communication templates across channels</h3>
      <div class="d-flex align-items-center justify-between gap-4">
        <div class="form-control-group m-0 mw-300px">
          <input
            type="search"
            class="form-control h-auto"
            placeholder="Search"
            [(ngModel)]="filter.searchTerm"
            (input)="onSearch($event)"
            name="searchTerm"
          />
        </div>
        <div class="form-control-group m-0 mw-150px">
          <select
            class="form-select"
            [(ngModel)]="filter.channel"
            (change)="onSearch()"
          >
            <option [ngValue]="null">All Channels</option>
            <option [ngValue]="'email'">Email</option>
            <option [ngValue]="'sms'">SMS</option>
            <option [ngValue]="'letter'">Letter</option>
          </select>
        </div>
        @if (hasAccess?.create) {
        <button
          class="btn btn-primary icon-btn d-flex align-items-center justify-content-center"
          [tooltip]="'Create Communication Template'"
          [routerLink]="['/communication/create-communication-template']"
        >
          <svg-icon src="assets/new/svgs/plus.svg"></svg-icon>
        </button>
        }
      </div>
    </div>
    <div class="card-content p-0 overflow-auto">
      <table class="table enc-table">
        <thead>
          <tr>
            <th>Template Name</th>
            <th>Channel</th>
            <th>Languages</th>
            <th>Created By</th>
            <th>Created On</th>
            <th>Count of Triggers</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading">
            <td colspan="3" class="text-center">
              Loading Communication Templates...
            </td>
          </tr>
          <tr *ngIf="!isLoading && !totalItems">
            <td colspan="3" class="text-center">
              No Communication Templates found
            </td>
          </tr>
          <tr *ngFor="let item of templates">
            @if (hasAccess?.view || hasAccess?.edit) {
            <td class="actions action-hover">
              <div class="d-flex align-items-center">
                <button
                  class="action-icon-button primary"
                  dropdown
                  dropdownToggle
                  container="body"
                >
                  <svg-icon
                    src="assets/new/svgs/table-icon-quick-action.svg"
                    class="action-icon"
                    [applyClass]="true"
                  >
                  </svg-icon>
                  <ul
                    *dropdownMenu
                    class="dropdown-menu dropdown-menu-left dropdown-menu-dark"
                    role="menu"
                  >
                    <!-- View -->
                    @if (hasAccess?.view) {
                    <li
                      role="menuitem"
                      [routerLink]="['./view-communication-template', item?.id]"
                    >
                      <span class="dropdown-item">View Template</span>
                    </li>
                    }
                    <!-- Edit -->
                    @if (hasAccess?.edit) {
                    <li
                      role="menuitem"
                      [routerLink]="['./view-communication-template', item?.id]"
                    >
                      <span class="dropdown-item">Edit Template</span>
                    </li>
                    }
                     <li
                      role="menuitem"
                      [routerLink]="['./view-communication-template', item?.id]"
                    >
                      <span class="dropdown-item">Disable Template</span>
                    </li>
                    <li
                      role="menuitem"
                      [routerLink]="['./view-communication-template', item?.id]"
                    >
                      <span class="dropdown-item">Enable Template</span>
                    </li>
                  </ul>
                </button>
                <div>
                  <div>{{ item.templateName }}</div>
                  <div class="opacity-50 mt-2">
                    {{ item.description }}
                  </div>
                </div>
              </div>
            </td>
            } @else {
            <td>
              <div>
                <div>{{ item.templateName }}</div>
                <div class="opacity-50 mt-2">
                  {{ item.description }}
                </div>
              </div>
            </td>
            }
            <td class="align-content-center">
              <span class="border border-2 px-3 py-1 rounded-5 me-2">
                {{ item.channel }}
              </span>
            </td>
            <td class="align-content-center">
              @for (lang of item.languages; track lang) {
              <span
                class="bg-box-primary border border-2 px-3 py-1 rounded-5 me-2"
              >
                {{ lang | uppercase }}
              </span>
              }
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="card-footer">
      <div class="enc-pagination">
        <div class="pagination-count">
          Showing
          <select
            name="itemsPerPage"
            [(ngModel)]="itemsPerPage"
            class="form-select"
            (change)="changeItemsPerPage()"
          >
            @for (optionNumber of [10, 15, 20, 30, 40, 50]; track optionNumber)
            {
            <option [value]="optionNumber">
              {{ optionNumber }}
            </option>
            }
          </select>
          Results
        </div>
        <pagination
          [totalItems]="totalItems"
          aria-label="Default pagination"
          [(ngModel)]="currentPage"
          [itemsPerPage]="itemsPerPage"
          (pageChanged)="pageChanged($event)"
          [boundaryLinks]="true"
        >
        </pagination>
      </div>
    </div>
  </div>
</div>
